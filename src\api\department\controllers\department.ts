/**
 * Controlador para el modelo Department
 * Maneja las operaciones CRUD para los departamentos
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::department.department', ({ strapi }) => ({
  /**
   * Buscar todos los departamentos con paginación
   */
  async find(ctx) {
    const result = await super.find(ctx);
    if (!result) {
      return { data: [], meta: { total: 0 } };
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Buscar un departamento por ID
   */
  async findOne(ctx) {
    const result = await super.findOne(ctx);
    if (!result) {
      return ctx.notFound('Departamento no encontrado');
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Crear un nuevo departamento
   */
  async create(ctx) {
    const result = await super.create(ctx);
    if (!result) {
      return ctx.badRequest('Error al crear el departamento');
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Actualizar un departamento
   */
  async update(ctx) {
    const result = await super.update(ctx);
    if (!result) {
      return ctx.notFound('Departamento no encontrado para actualizar');
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Eliminar un departamento
   */
  async delete(ctx) {
    const result = await super.delete(ctx);
    if (!result) {
      return ctx.notFound('Departamento no encontrado para eliminar');
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Obtener departamentos activos
   */
  async getActive(ctx) {
    try {
      const departments = await strapi.entityService.findMany('api::department.department', {
        filters: {
          isActive: true
        },
        sort: { name: 'asc' }
      });

      return {
        data: departments,
        meta: {
          total: departments.length
        }
      };
    } catch (error) {
      ctx.throw(500, `Error al obtener departamentos activos: ${error.message}`);
    }
  },

  /**
   * Obtener estadísticas de eventos por departamento
   */
  async getEventStats(ctx) {
    try {
      const { id } = ctx.params;
      
      // Verificar que el departamento existe
      const department = await strapi.entityService.findOne('api::department.department', id);
      
      if (!department) {
        return ctx.notFound('Departamento no encontrado');
      }

      // Obtener eventos relacionados con este departamento
      const events = await strapi.entityService.findMany('api::event.event', {
        filters: {
          invitingDepartment: {
            id: id
          }
        },
        populate: {
          eventType: true,
          attendanceRecords: true
        }
      }) as any[];

      // Calcular estadísticas
      const stats = {
        totalEvents: events.length,
        eventsByStatus: {},
        eventsByType: {},
        totalAttendees: 0
      };

      events.forEach(event => {
        // Contar por estado
        const status = event.status || 'unknown';
        stats.eventsByStatus[status] = (stats.eventsByStatus[status] || 0) + 1;

        // Contar por tipo
        const eventType = event.eventType?.name || 'Sin tipo';
        stats.eventsByType[eventType] = (stats.eventsByType[eventType] || 0) + 1;

        // Contar asistentes
        if (event.attendanceRecords) {
          stats.totalAttendees += event.attendanceRecords.length;
        }
      });

      return {
        data: {
          department: {
            id: department.id,
            name: department.name,
            description: department.description
          },
          statistics: stats,
          events: events.map(event => ({
            id: event.id,
            title: event.title,
            date: event.date,
            status: event.status,
            eventType: event.eventType?.name,
            attendeesCount: event.attendanceRecords?.length || 0
          }))
        }
      };
    } catch (error) {
      ctx.throw(500, `Error al obtener estadísticas del departamento: ${error.message}`);
    }
  }
}));