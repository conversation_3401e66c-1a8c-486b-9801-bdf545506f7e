# 📋 API Reference - SCMP

## Introducción

Esta documentación describe la API REST del Sistema de Competencia y Ministerio Pastoral (SCMP). La API está construida sobre Strapi v5 y sigue los estándares REST.

## Base URL

```
# Desarrollo
http://localhost:1337/api

# Producción
https://your-domain.com/api
```

## Autenticación

### JWT Token

```bash
# Obtener token
POST /auth/local
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "password": "password123"
}

# Usar token en requests
GET /api/churches
Authorization: Bearer <your-jwt-token>
```

### API Key

```bash
# Usar API Key
GET /api/churches
Authorization: Bearer <your-api-key>
```

## Formato de Respuesta

### Respuesta Exitosa

```json
{
  "data": [
    {
      "id": 1,
      "documentId": "church-uuid-123",
      "attributes": {
        "name": "Iglesia Central",
        "address": "Calle Principal 123",
        "phone": "******-555-0123",
        "email": "<EMAIL>",
        "createdAt": "2025-01-28T10:00:00.000Z",
        "updatedAt": "2025-01-28T10:00:00.000Z",
        "publishedAt": "2025-01-28T10:00:00.000Z",
        "locale": "es"
      }
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 43,
      "total": 1075
    }
  }
}
```

### Respuesta de Error

```json
{
  "error": {
    "status": 400,
    "name": "ValidationError",
    "message": "Invalid request parameters",
    "details": {
      "errors": [
        {
          "path": ["name"],
          "message": "Name is required",
          "name": "ValidationError"
        }
      ]
    }
  }
}
```

## Parámetros de Consulta

### Paginación

```bash
# Página específica
GET /api/churches?pagination[page]=2&pagination[pageSize]=50

# Sin paginación (máximo 100 registros)
GET /api/churches?pagination[page]=1&pagination[pageSize]=100
```

### Filtros

```bash
# Filtro por nombre (contiene)
GET /api/churches?filters[name][$contains]=Central

# Filtro por nombre exacto
GET /api/churches?filters[name][$eq]=Iglesia Central

# Filtros múltiples
GET /api/churches?filters[name][$contains]=Central&filters[address][$contains]=Santo Domingo

# Filtro por relación
GET /api/churches?filters[district][name][$contains]=Distrito 1
```

#### Operadores de Filtro

| Operador | Descripción | Ejemplo |
|----------|-------------|----------|
| `$eq` | Igual a | `filters[name][$eq]=Iglesia Central` |
| `$ne` | No igual a | `filters[name][$ne]=Iglesia Central` |
| `$contains` | Contiene | `filters[name][$contains]=Central` |
| `$notContains` | No contiene | `filters[name][$notContains]=Cerrada` |
| `$startsWith` | Empieza con | `filters[name][$startsWith]=Iglesia` |
| `$endsWith` | Termina con | `filters[name][$endsWith]=Central` |
| `$gt` | Mayor que | `filters[id][$gt]=100` |
| `$gte` | Mayor o igual | `filters[id][$gte]=100` |
| `$lt` | Menor que | `filters[id][$lt]=100` |
| `$lte` | Menor o igual | `filters[id][$lte]=100` |
| `$in` | En lista | `filters[id][$in][0]=1&filters[id][$in][1]=2` |
| `$notIn` | No en lista | `filters[id][$notIn][0]=1&filters[id][$notIn][1]=2` |
| `$null` | Es nulo | `filters[email][$null]=true` |
| `$notNull` | No es nulo | `filters[email][$notNull]=true` |

### Ordenamiento

```bash
# Ordenar por nombre ascendente
GET /api/churches?sort=name:asc

# Ordenar por nombre descendente
GET /api/churches?sort=name:desc

# Ordenamiento múltiple
GET /api/churches?sort[0]=name:asc&sort[1]=createdAt:desc
```

### Población de Relaciones

```bash
# Poblar relación directa
GET /api/churches?populate=district

# Poblar relaciones anidadas
GET /api/churches?populate=district.zone.field

# Poblar múltiples relaciones
GET /api/churches?populate[0]=district&populate[1]=pastor

# Poblar con filtros
GET /api/churches?populate[district][filters][name][$contains]=Centro
```

### Selección de Campos

```bash
# Seleccionar campos específicos
GET /api/churches?fields[0]=name&fields[1]=address&fields[2]=phone

# Seleccionar campos de relaciones
GET /api/churches?populate=district&fields[0]=name&fields[1]=district.name
```

## Endpoints

## 1. Fields (Campos/Asociaciones)

### Listar Fields

```http
GET /api/fields
```

**Parámetros de consulta:**
- `pagination[page]`: Número de página (default: 1)
- `pagination[pageSize]`: Tamaño de página (default: 25, max: 100)
- `filters[name][$contains]`: Filtrar por nombre
- `filters[acronym][$eq]`: Filtrar por acrónimo
- `filters[type][$eq]`: Filtrar por tipo
- `sort`: Ordenamiento (ej: `name:asc`)
- `populate`: Poblar relaciones (ej: `zones`)

**Ejemplo de respuesta:**

```json
{
  "data": [
    {
      "id": 1,
      "documentId": "acd-001",
      "attributes": {
        "name": "Asociación Central Dominicana",
        "acronym": "ACD",
        "type": "association",
        "createdAt": "2025-01-28T10:00:00.000Z",
        "updatedAt": "2025-01-28T10:00:00.000Z",
        "publishedAt": "2025-01-28T10:00:00.000Z",
        "locale": "es"
      }
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 4
    }
  }
}
```

### Obtener Field por ID

```http
GET /api/fields/{id}
```

**Parámetros:**
- `id`: ID numérico o documentId del field

### Crear Field

```http
POST /api/fields
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": {
    "name": "Nueva Asociación",
    "acronym": "NA",
    "type": "association"
  }
}
```

### Actualizar Field

```http
PUT /api/fields/{id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": {
    "name": "Asociación Actualizada",
    "acronym": "AA"
  }
}
```

### Eliminar Field

```http
DELETE /api/fields/{id}
Authorization: Bearer <token>
```

## 2. Zones (Zonas)

### Listar Zones

```http
GET /api/zones
```

**Parámetros de consulta:**
- `filters[name][$contains]`: Filtrar por nombre
- `filters[field][name][$eq]`: Filtrar por nombre del field
- `populate`: Poblar relaciones (`field`, `districts`)

**Ejemplo con relaciones:**

```bash
GET /api/zones?populate=field,districts&filters[field][name][$contains]=Central
```

### Obtener Zone por ID

```http
GET /api/zones/{id}?populate=field,districts
```

### Crear Zone

```http
POST /api/zones
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": {
    "name": "Nueva Zona",
    "field": 1
  }
}
```

## 3. Districts (Distritos)

### Listar Districts

```http
GET /api/districts
```

**Parámetros de consulta:**
- `filters[name][$contains]`: Filtrar por nombre
- `filters[zone][name][$eq]`: Filtrar por nombre de la zona
- `populate`: Poblar relaciones (`zone`, `churches`)

**Ejemplo con jerarquía completa:**

```bash
GET /api/districts?populate=zone.field,churches&filters[zone][field][name][$eq]=Asociación Central Dominicana
```

### Obtener District por ID

```http
GET /api/districts/{id}?populate=zone.field,churches
```

### Crear District

```http
POST /api/districts
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": {
    "name": "Nuevo Distrito",
    "zone": 1
  }
}
```

## 4. Churches (Iglesias)

### Listar Churches

```http
GET /api/churches
```

**Parámetros de consulta:**
- `filters[name][$contains]`: Filtrar por nombre
- `filters[address][$contains]`: Filtrar por dirección
- `filters[district][name][$eq]`: Filtrar por nombre del distrito
- `populate`: Poblar relaciones (`district`)

**Ejemplos de consultas:**

```bash
# Iglesias en Santo Domingo
GET /api/churches?filters[address][$contains]=Santo Domingo

# Iglesias de un distrito específico
GET /api/churches?filters[district][name][$eq]=Distrito Central&populate=district.zone.field

# Iglesias con información completa de jerarquía
GET /api/churches?populate=district.zone.field&pagination[pageSize]=10
```

### Obtener Church por ID

```http
GET /api/churches/{id}?populate=district.zone.field
```

**Ejemplo de respuesta:**

```json
{
  "data": {
    "id": 1,
    "documentId": "church-uuid-123",
    "attributes": {
      "name": "Iglesia Central",
      "address": "Calle Principal 123, Santo Domingo",
      "phone": "******-555-0123",
      "email": "<EMAIL>",
      "createdAt": "2025-01-28T10:00:00.000Z",
      "updatedAt": "2025-01-28T10:00:00.000Z",
      "publishedAt": "2025-01-28T10:00:00.000Z",
      "locale": "es",
      "district": {
        "data": {
          "id": 1,
          "attributes": {
            "name": "Distrito Central",
            "zone": {
              "data": {
                "id": 1,
                "attributes": {
                  "name": "Zona Metropolitana",
                  "field": {
                    "data": {
                      "id": 1,
                      "attributes": {
                        "name": "Asociación Central Dominicana",
                        "acronym": "ACD"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  "meta": {}
}
```

### Crear Church

```http
POST /api/churches
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": {
    "name": "Nueva Iglesia",
    "address": "Calle Nueva 456, Santiago",
    "phone": "******-555-0456",
    "email": "<EMAIL>",
    "district": 1
  }
}
```

### Actualizar Church

```http
PUT /api/churches/{id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": {
    "name": "Iglesia Actualizada",
    "phone": "******-555-0789"
  }
}
```

### Eliminar Church

```http
DELETE /api/churches/{id}
Authorization: Bearer <token>
```

## Consultas Avanzadas

### 1. Estadísticas por Field

```bash
# Obtener todos los fields con sus zonas, distritos e iglesias
GET /api/fields?populate[zones][populate][districts][populate]=churches
```

### 2. Búsqueda Global

```bash
# Buscar iglesias por múltiples criterios
GET /api/churches?filters[$or][0][name][$contains]=Central&filters[$or][1][address][$contains]=Central&populate=district.zone.field
```

### 3. Iglesias por Ubicación

```bash
# Iglesias en Santo Domingo con jerarquía completa
GET /api/churches?filters[address][$contains]=Santo Domingo&populate=district.zone.field&sort=name:asc
```

### 4. Conteo de Entidades

```bash
# Obtener conteo total sin datos
GET /api/churches?pagination[pageSize]=1&fields[0]=id
```

## Códigos de Estado HTTP

| Código | Descripción | Uso |
|--------|-------------|-----|
| 200 | OK | Solicitud exitosa |
| 201 | Created | Recurso creado exitosamente |
| 204 | No Content | Eliminación exitosa |
| 400 | Bad Request | Datos de entrada inválidos |
| 401 | Unauthorized | Token de autenticación requerido |
| 403 | Forbidden | Permisos insuficientes |
| 404 | Not Found | Recurso no encontrado |
| 422 | Unprocessable Entity | Error de validación |
| 500 | Internal Server Error | Error del servidor |

## Rate Limiting

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1643723400
```

- **Límite**: 1000 requests por hora por IP
- **Headers**: Información de rate limiting en headers de respuesta
- **Exceso**: HTTP 429 Too Many Requests

## Ejemplos de Uso

### JavaScript/Node.js

```javascript
// Usando fetch
const response = await fetch('http://localhost:1337/api/churches?populate=district.zone.field', {
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data.data); // Array de iglesias

// Crear nueva iglesia
const newChurch = await fetch('http://localhost:1337/api/churches', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    data: {
      name: 'Nueva Iglesia',
      address: 'Calle Nueva 123',
      district: 1
    }
  })
});
```

### Python

```python
import requests

# Configuración
BASE_URL = 'http://localhost:1337/api'
TOKEN = 'your-jwt-token'

headers = {
    'Authorization': f'Bearer {TOKEN}',
    'Content-Type': 'application/json'
}

# Obtener iglesias
response = requests.get(
    f'{BASE_URL}/churches',
    headers=headers,
    params={
        'populate': 'district.zone.field',
        'pagination[pageSize]': 50
    }
)

churches = response.json()['data']

# Crear iglesia
new_church_data = {
    'data': {
        'name': 'Nueva Iglesia',
        'address': 'Calle Nueva 123',
        'district': 1
    }
}

response = requests.post(
    f'{BASE_URL}/churches',
    headers=headers,
    json=new_church_data
)
```

### cURL

```bash
# Obtener iglesias
curl -X GET "http://localhost:1337/api/churches?populate=district.zone.field" \
  -H "Authorization: Bearer your-jwt-token"

# Crear iglesia
curl -X POST "http://localhost:1337/api/churches" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "name": "Nueva Iglesia",
      "address": "Calle Nueva 123",
      "district": 1
    }
  }'
```

## Webhooks

### Configuración

```javascript
// config/plugins.js
module.exports = {
  webhooks: {
    populateRelations: ['district.zone.field'],
    defaultHeaders: {
      'Custom-Header': 'value'
    }
  }
};
```

### Eventos Disponibles

- `entry.create`: Cuando se crea una entidad
- `entry.update`: Cuando se actualiza una entidad
- `entry.delete`: Cuando se elimina una entidad
- `entry.publish`: Cuando se publica una entidad
- `entry.unpublish`: Cuando se despublica una entidad

### Payload de Webhook

```json
{
  "event": "entry.create",
  "createdAt": "2025-01-28T10:00:00.000Z",
  "model": "church",
  "entry": {
    "id": 1,
    "documentId": "church-uuid-123",
    "name": "Nueva Iglesia",
    "address": "Calle Nueva 123",
    "district": {
      "id": 1,
      "name": "Distrito Central"
    }
  }
}
```

## Colección de Postman

```json
{
  "info": {
    "name": "SCMP API",
    "description": "Colección de endpoints para SCMP",
    "version": "1.0.0"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{jwt_token}}",
        "type": "string"
      }
    ]
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:1337/api"
    },
    {
      "key": "jwt_token",
      "value": "your-jwt-token"
    }
  ]
}
```

## Troubleshooting

### Errores Comunes

#### 1. Error 401 - Unauthorized

```json
{
  "error": {
    "status": 401,
    "name": "UnauthorizedError",
    "message": "Missing or invalid credentials"
  }
}
```

**Solución**: Verificar que el token JWT sea válido y esté incluido en el header.

#### 2. Error 404 - Not Found

```json
{
  "error": {
    "status": 404,
    "name": "NotFoundError",
    "message": "Not Found"
  }
}
```

**Solución**: Verificar que el endpoint y el ID sean correctos.

#### 3. Error 400 - Validation Error

```json
{
  "error": {
    "status": 400,
    "name": "ValidationError",
    "message": "Invalid request body",
    "details": {
      "errors": [
        {
          "path": ["name"],
          "message": "Name is required"
        }
      ]
    }
  }
}
```

**Solución**: Verificar que todos los campos requeridos estén presentes y sean válidos.

## Changelog

### v1.0.0 (2025-01-28)
- ✅ Documentación inicial de la API
- ✅ Endpoints para Fields, Zones, Districts, Churches
- ✅ Ejemplos de uso en múltiples lenguajes
- ✅ Guía de troubleshooting
- ✅ Colección de Postman

## Recursos Adicionales

- [Documentación de Strapi REST API](https://docs.strapi.io/dev-docs/api/rest)
- [Postman Collection](./postman-collection.json)
- [OpenAPI Specification](./openapi.yaml)
- [Guía de Autenticación](../security/authentication.md)