const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// Configuración de la base de datos desde .env
const dbConfig = {
  host: 'aws-0-us-east-2.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.ktykxlboodjvnlabbwoy',
  password: 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};

async function manualSeed() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');
    
    // Leer datos del archivo JSON
    const seedDataPath = path.join(__dirname, 'database', 'seeds', 'initial-data.json');
    const seedData = JSON.parse(fs.readFileSync(seedDataPath, 'utf8'));
    
    console.log('📊 Datos cargados:');
    console.log(`   - Fields: ${seedData.fields.length}`);
    console.log(`   - Zones: ${seedData.zones.length}`);
    console.log(`   - Districts: ${seedData.districts.length}`);
    console.log(`   - Churches: ${seedData.churches.length}`);
    
    // 1. Insertar Fields primero (no tienen dependencias)
    console.log('\n📝 Insertando Fields...');
    let fieldsInserted = 0;
    const fieldIds = {};
    
    for (const field of seedData.fields) {
      // Verificar si el field ya existe
      const existingFields = await client.query('SELECT * FROM fields WHERE name = $1', [field.name]);
      
      if (existingFields.rows.length === 0) {
        const insertResult = await client.query(
          `INSERT INTO fields (document_id, name, acronym, type, published_at, locale, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
          [
            field.documentId,
            field.name,
            field.acronym,
            field.type,
            field.publishedAt,
            field.locale,
            new Date(),
            new Date()
          ]
        );
        
        fieldIds[field.name] = insertResult.rows[0].id;
        fieldsInserted++;
        console.log(`   ✅ Field insertado: ${field.name}`);
      } else {
        fieldIds[field.name] = existingFields.rows[0].id;
        console.log(`   ⚠️ Field ya existe: ${field.name}`);
      }
    }
    console.log(`   📈 Total Fields insertados: ${fieldsInserted}`);
    
    // 2. Insertar Zones (dependen de Fields)
    console.log('\n🗺️ Insertando Zones...');
    let zonesInserted = 0;
    const zoneIds = {};
    
    for (const zone of seedData.zones) {
       // Buscar el field relacionado primero
       const fieldId = fieldIds[zone.field_name];
       if (!fieldId) {
         console.warn(`   ⚠️ Field '${zone.field_name}' no encontrado para zone '${zone.name}'`);
         continue;
       }
       
       // Verificar si la zone ya existe dentro del mismo field usando JOIN con tabla de unión
       const existingZones = await client.query(
         `SELECT zones.* FROM zones 
          JOIN zones_field_lnk ON zones.id = zones_field_lnk.zone_id 
          WHERE zones.name = $1 AND zones_field_lnk.field_id = $2`,
         [zone.name, fieldId]
       );
       
       if (existingZones.rows.length === 0) {
         const insertResult = await client.query(
           `INSERT INTO zones (document_id, name, published_at, locale, created_at, updated_at) 
            VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
           [
             zone.documentId,
             zone.name,
             zone.publishedAt,
             zone.locale,
             new Date(),
             new Date()
           ]
         );
         
         // Crear la relación en la tabla de unión
         await client.query(
           'INSERT INTO zones_field_lnk (zone_id, field_id) VALUES ($1, $2)',
           [insertResult.rows[0].id, fieldId]
         );
         
         // Usar clave compuesta para identificar zones únicas
         const zoneKey = `${zone.field_name}|${zone.name}`;
         zoneIds[zoneKey] = insertResult.rows[0].id;
         zonesInserted++;
         console.log(`   ✅ Zone insertada: ${zone.name} en field ${zone.field_name}`);
       } else {
         // Usar clave compuesta para zones existentes también
         const zoneKey = `${zone.field_name}|${zone.name}`;
         zoneIds[zoneKey] = existingZones.rows[0].id;
         console.log(`   ⚠️ Zone ya existe: ${zone.name} en field ${zone.field_name}`);
       }
     }
    console.log(`   📈 Total Zones insertadas: ${zonesInserted}`);
    
    // 3. Insertar Districts (dependen de Zones)
    console.log('\n🏘️ Insertando Districts...');
    let districtsInserted = 0;
    const districtIds = {};
    
    for (const district of seedData.districts) {
       // Primero encontrar el field_name desde la zone correspondiente
       const zoneData = seedData.zones.find(z => z.name === district.zone_name);
       if (!zoneData) {
         console.warn(`   ⚠️ Zone '${district.zone_name}' no encontrada en datos para district '${district.name}'`);
         continue;
       }
       
       // Buscar la zone relacionada usando clave compuesta
       const zoneKey = `${zoneData.field_name}|${district.zone_name}`;
       const zoneId = zoneIds[zoneKey];
       if (!zoneId) {
         console.warn(`   ⚠️ Zone '${district.zone_name}' en field '${zoneData.field_name}' no encontrada para district '${district.name}'`);
         continue;
       }
       
       // Verificar si el district ya existe dentro de la misma zone usando JOIN con tabla de unión
       const existingDistricts = await client.query(
         `SELECT districts.* FROM districts 
          JOIN districts_zone_lnk ON districts.id = districts_zone_lnk.district_id 
          WHERE districts.name = $1 AND districts_zone_lnk.zone_id = $2`,
         [district.name, zoneId]
       );
       
       if (existingDistricts.rows.length === 0) {
         const insertResult = await client.query(
           `INSERT INTO districts (document_id, name, published_at, locale, created_at, updated_at) 
            VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
           [
             district.documentId,
             district.name,
             district.publishedAt,
             district.locale,
             new Date(),
             new Date()
           ]
         );
         
         // Crear la relación en la tabla de unión
         await client.query(
           'INSERT INTO districts_zone_lnk (district_id, zone_id) VALUES ($1, $2)',
           [insertResult.rows[0].id, zoneId]
         );
         
         // Usar clave compuesta para identificar districts únicos
         const districtKey = `${zoneData.field_name}|${district.zone_name}|${district.name}`;
         districtIds[districtKey] = insertResult.rows[0].id;
         districtsInserted++;
         console.log(`   ✅ District insertado: ${district.name} en zone ${district.zone_name}`);
       } else {
         // Usar clave compuesta para districts existentes también
         const districtKey = `${zoneData.field_name}|${district.zone_name}|${district.name}`;
         districtIds[districtKey] = existingDistricts.rows[0].id;
         console.log(`   ⚠️ District ya existe: ${district.name} en zone ${district.zone_name}`);
       }
     }
    console.log(`   📈 Total Districts insertados: ${districtsInserted}`);
    
    // 4. Insertar Churches (dependen de Districts)
    console.log('\n⛪ Insertando Churches...');
    let churchesInserted = 0;
    let churchesSkipped = 0;
    let churchesErrors = 0;
    
    console.log(`   📊 Total churches a procesar: ${seedData.churches.length}`);
    
    for (let i = 0; i < seedData.churches.length; i++) {
       const church = seedData.churches[i];
       
       try {
         // Mostrar progreso cada 100 iglesias
         if (i % 100 === 0) {
           console.log(`   📈 Progreso: ${i}/${seedData.churches.length} churches procesadas`);
         }
         
         // Primero encontrar el field_name desde la zone correspondiente
         const churchZoneData = seedData.zones.find(z => z.name === church.zone_name);
         if (!churchZoneData) {
           console.warn(`   ⚠️ Zone '${church.zone_name}' no encontrada en datos para church '${church.name}'`);
           churchesErrors++;
           continue;
         }
         
         // Buscar el district relacionado usando clave compuesta
         const districtKey = `${churchZoneData.field_name}|${church.zone_name}|${church.district_name}`;
         const districtId = districtIds[districtKey];
         if (!districtId) {
           console.warn(`   ⚠️ District '${church.district_name}' en zone '${church.zone_name}' no encontrado para church '${church.name}'`);
           churchesErrors++;
           continue;
         }
         
         // Verificar si la church ya existe dentro del mismo district usando JOIN con tabla de unión
         const existingChurches = await client.query(
           `SELECT churches.* FROM churches 
            JOIN churches_district_lnk ON churches.id = churches_district_lnk.church_id 
            WHERE churches.name = $1 AND churches_district_lnk.district_id = $2`,
           [church.name, districtId]
         );
         
         if (existingChurches.rows.length === 0) {
           const insertResult = await client.query(
             `INSERT INTO churches (document_id, name, published_at, locale, created_at, updated_at) 
              VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
             [
               church.documentId,
               church.name,
               church.publishedAt,
               church.locale,
               new Date(),
               new Date()
             ]
           );
           
           // Crear la relación en la tabla de unión
           await client.query(
             'INSERT INTO churches_district_lnk (church_id, district_id) VALUES ($1, $2)',
             [insertResult.rows[0].id, districtId]
           );
           
           churchesInserted++;
           if (churchesInserted % 50 === 0) {
             console.log(`   ✅ ${churchesInserted} churches insertadas hasta ahora...`);
           }
         } else {
           churchesSkipped++;
         }
       } catch (error) {
         console.error(`   ❌ Error insertando church '${church.name}':`, error.message);
         churchesErrors++;
       }
     }
    
    console.log(`   📈 Resumen Churches:`);
    console.log(`     - Insertadas: ${churchesInserted}`);
    console.log(`     - Ya existían: ${churchesSkipped}`);
    console.log(`     - Errores: ${churchesErrors}`);
    console.log(`     - Total procesadas: ${churchesInserted + churchesSkipped + churchesErrors}/${seedData.churches.length}`);
    
    console.log('\n✅ Inserción manual de datos completada exitosamente!');
    console.log(`📈 Resumen final:`);
    console.log(`   - Fields: ${fieldsInserted} insertados`);
    console.log(`   - Zones: ${zonesInserted} insertadas`);
    console.log(`   - Districts: ${districtsInserted} insertados`);
    console.log(`   - Churches: ${churchesInserted} insertadas`);
    
  } catch (error) {
    console.error('❌ Error durante la inserción manual:', error);
  } finally {
    await client.end();
    console.log('\n🔌 Conexión cerrada');
  }
}

manualSeed();