{"kind": "collectionType", "collectionName": "departments", "info": {"singularName": "department", "pluralName": "departments", "displayName": "Department", "description": "Departamentos que pueden invitar a eventos"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "description": {"type": "text"}, "isActive": {"type": "boolean", "default": true, "required": true}, "events": {"type": "relation", "relation": "oneToMany", "target": "api::event.event", "mappedBy": "invitingDepartment"}}}