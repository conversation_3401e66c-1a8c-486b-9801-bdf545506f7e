'use strict';

module.exports = async ({ strapi }) => {
  const authenticatedRole = await strapi.db
    .query('plugin::users-permissions.role')
    .findOne({ where: { type: 'authenticated' } });
  const publicRole = await strapi.db
    .query('plugin::users-permissions.role')
    .findOne({ where: { type: 'public' } });

  const grant = async (roleId, action) => {
    const permissionService = strapi.query('plugin::users-permissions.permission');
    const existing = await permissionService.findOne({ where: { role: roleId, action } });
    if (existing) {
      if (!existing.enabled) {
        await permissionService.update({ where: { id: existing.id }, data: { enabled: true } });
      }
      return;
    }
    await permissionService.create({ data: { action, role: roleId, enabled: true } });
  };

  const authenticatedActions = [
    'api::event.event.find',
    'api::event.event.findOne',
    'api::event-type.event-type.find',
    'api::event-type.event-type.findOne',
    'api::field.field.find',
    'api::zone.zone.find',
    'api::district.district.find',
    'api::church.church.find',
    'api::attendance-record.attendance-record.create',
    'api::attendance-record.attendance-record.update',
    'api::attendance-record.attendance-record.delete',
    'api::event.event.getHierarchicalData',
    'api::event.event.recordAttendance',
    'api::event.event.completeKioskSession',
    'api::event.event.updateEventStatus',
    'api::event.event.findForUser',
    'api::event.event.justifyAbsence',
    'api::event.event.importParticipants',
    'api::event.event.validateParticipant',
    'api::event.event.addSession',
    'api::event.event.updateSession',
    'api::event.event.deleteSession',
    'api::account.account.updateProfile',
    'api::account.account.changePassword',
    'api::participant.participant.find',
    'api::participant.participant.findOne',
    'api::participant.participant.create',
    'api::participant.participant.update',
    'api::participant.participant.delete',
  ];

  for (const action of authenticatedActions) {
    await grant(authenticatedRole.id, action);
  }

  await grant(publicRole.id, 'api::event.event.selfRegister');
};