/**
 * Controlador para el modelo EventType
 * Maneja las operaciones CRUD para los tipos de eventos
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::event-type.event-type', ({ strapi }) => ({
  /**
   * Buscar todos los tipos de eventos con paginación
   */
  async find(ctx) {
    const { data, meta } = await super.find(ctx);
    return { data, meta };
  },

  /**
   * Buscar un tipo de evento por ID
   */
  async findOne(ctx) {
    const { data, meta } = await super.findOne(ctx);
    return { data, meta };
  },

  /**
   * Crear un nuevo tipo de evento
   */
  async create(ctx) {
    const { data, meta } = await super.create(ctx);
    return { data, meta };
  },

  /**
   * Actualizar un tipo de evento existente
   */
  async update(ctx) {
    const { data, meta } = await super.update(ctx);
    return { data, meta };
  },

  /**
   * Eliminar un tipo de evento
   */
  async delete(ctx) {
    const { data, meta } = await super.delete(ctx);
    return { data, meta };
  },

  /**
   * Obtener estadísticas de eventos por tipo
   */
  async getStats(ctx) {
    const { id } = ctx.params;
    
    try {
      const stats = await strapi.service('api::event-type.event-type').getEventStatsByType(id);
      
      if (!stats) {
        return ctx.notFound('Tipo de evento no encontrado');
      }
      
      return { data: stats };
    } catch (error) {
      return ctx.badRequest('Error al obtener estadísticas del tipo de evento');
    }
  }
}));