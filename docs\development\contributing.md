# 🤝 Guía de Contribución - SCMP

## Bienvenido

¡Gracias por tu interés en contribuir al Sistema de Competencia y Ministerio Pastoral (SCMP)! Esta guía te ayudará a entender cómo puedes contribuir de manera efectiva al proyecto.

## Código de Conducta

### Nuestros Valores

- **Respeto**: Tratamos a todos con dignidad y respeto
- **Colaboración**: Trabajamos juntos hacia objetivos comunes
- **Excelencia**: Nos esforzamos por la calidad en todo lo que hacemos
- **Transparencia**: Comunicamos abierta y honestamente
- **Servicio**: Nuestro trabajo sirve a la misión de la iglesia

### Comportamiento Esperado

- Usar lenguaje inclusivo y respetuoso
- Ser constructivo en críticas y sugerencias
- Enfocarse en lo que es mejor para la comunidad
- Mostrar empatía hacia otros miembros de la comunidad
- Aceptar críticas constructivas con gracia

## Formas de Contribuir

### 1. Reportar Bugs

#### Antes de Reportar

- Verifica que el bug no haya sido reportado anteriormente
- Asegúrate de estar usando la versión más reciente
- Reproduce el bug en un entorno limpio

#### Información a Incluir

```markdown
**Descripción del Bug**
Una descripción clara y concisa del problema.

**Pasos para Reproducir**
1. Ve a '...'
2. Haz clic en '....'
3. Desplázate hacia abajo hasta '....'
4. Ve el error

**Comportamiento Esperado**
Una descripción clara de lo que esperabas que sucediera.

**Capturas de Pantalla**
Si es aplicable, agrega capturas de pantalla para ayudar a explicar el problema.

**Información del Entorno**
- OS: [ej. Windows 11]
- Navegador: [ej. Chrome 120]
- Versión de Node.js: [ej. 18.17.0]
- Versión del Proyecto: [ej. 1.0.0]

**Contexto Adicional**
Agrega cualquier otro contexto sobre el problema aquí.
```

### 2. Sugerir Mejoras

#### Proceso de Sugerencias

1. **Investigación**: Verifica que la funcionalidad no exista
2. **Discusión**: Abre un issue para discutir la idea
3. **Especificación**: Define claramente los requisitos
4. **Implementación**: Desarrolla la funcionalidad
5. **Testing**: Prueba exhaustivamente
6. **Documentación**: Actualiza la documentación

#### Template para Sugerencias

```markdown
**¿Tu solicitud de funcionalidad está relacionada con un problema?**
Una descripción clara de cuál es el problema. Ej. Siempre me frustra cuando [...]

**Describe la solución que te gustaría**
Una descripción clara y concisa de lo que quieres que suceda.

**Describe alternativas que hayas considerado**
Una descripción clara de cualquier solución o funcionalidad alternativa que hayas considerado.

**Contexto adicional**
Agrega cualquier otro contexto o capturas de pantalla sobre la solicitud de funcionalidad aquí.
```

### 3. Contribuir con Código

#### Configuración del Entorno

```bash
# Fork del repositorio
git clone https://github.com/tu-usuario/scmp-cm.git
cd scmp-cm

# Configurar upstream
git remote add upstream https://github.com/original-repo/scmp-cm.git

# Instalar dependencias
npm install

# Configurar entorno
cp .env.example .env
# Editar .env con tus configuraciones

# Verificar que todo funciona
npm run dev
```

#### Flujo de Trabajo

1. **Crear Branch**
   ```bash
   git checkout -b feature/nueva-funcionalidad
   # o
   git checkout -b fix/correccion-bug
   ```

2. **Desarrollar**
   - Escribe código limpio y bien documentado
   - Sigue las convenciones del proyecto
   - Agrega tests para nueva funcionalidad

3. **Testing**
   ```bash
   npm run lint
   npm test
   npm run build
   ```

4. **Commit**
   ```bash
   git add .
   git commit -m "feat: agregar nueva funcionalidad"
   ```

5. **Push y Pull Request**
   ```bash
   git push origin feature/nueva-funcionalidad
   ```

### 4. Mejorar Documentación

#### Tipos de Documentación

- **Documentación Técnica**: APIs, arquitectura, configuración
- **Guías de Usuario**: Cómo usar el sistema
- **Tutoriales**: Paso a paso para tareas específicas
- **Ejemplos**: Código de ejemplo y casos de uso

#### Estándares de Documentación

- Usar Markdown para toda la documentación
- Incluir ejemplos de código cuando sea relevante
- Mantener un tono claro y profesional
- Actualizar documentación con cambios de código

## Estándares de Código

### Convenciones de Nomenclatura

```javascript
// Variables y funciones: camelCase
const userName = 'john_doe';
function getUserData() {}

// Constantes: UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';

// Clases: PascalCase
class UserManager {}

// Archivos: kebab-case
// user-service.js
// api-controller.js
```

### Estructura de Archivos

```javascript
// 1. Imports de librerías externas
import express from 'express';
import { validationResult } from 'express-validator';

// 2. Imports internos
import { UserService } from '../services/user-service.js';
import { logger } from '../utils/logger.js';

// 3. Constantes
const DEFAULT_PAGE_SIZE = 25;

// 4. Funciones auxiliares
function validateInput(data) {
  // ...
}

// 5. Función/clase principal
export class UserController {
  // ...
}
```

### Comentarios y Documentación

```javascript
/**
 * Obtiene una lista paginada de usuarios
 * @param {Object} options - Opciones de consulta
 * @param {number} options.page - Número de página (1-based)
 * @param {number} options.pageSize - Tamaño de página
 * @param {string} options.search - Término de búsqueda
 * @returns {Promise<Object>} Lista paginada de usuarios
 */
async function getUsers({ page = 1, pageSize = 25, search = '' }) {
  // Validar parámetros de entrada
  if (page < 1) {
    throw new Error('El número de página debe ser mayor a 0');
  }

  // Construir consulta
  const query = buildUserQuery({ search });
  
  // Ejecutar consulta con paginación
  return await executeQuery(query, { page, pageSize });
}
```

### Testing

#### Estructura de Tests

```javascript
// tests/services/user-service.test.js
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { UserService } from '../../src/services/user-service.js';

describe('UserService', () => {
  let userService;

  beforeEach(() => {
    userService = new UserService();
  });

  afterEach(() => {
    // Cleanup
  });

  describe('getUsers', () => {
    it('should return paginated users', async () => {
      // Arrange
      const options = { page: 1, pageSize: 10 };

      // Act
      const result = await userService.getUsers(options);

      // Assert
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(result.data).toBeInstanceOf(Array);
    });

    it('should handle search parameter', async () => {
      // Test implementation
    });

    it('should throw error for invalid page number', async () => {
      // Test implementation
    });
  });
});
```

#### Cobertura de Tests

- **Funciones Públicas**: 100% de cobertura
- **Casos Edge**: Manejar casos límite
- **Errores**: Probar manejo de errores
- **Integración**: Tests de integración para APIs

## Convenciones de Git

### Mensajes de Commit

Usamos [Conventional Commits](https://www.conventionalcommits.org/):

```
<tipo>[ámbito opcional]: <descripción>

[cuerpo opcional]

[pie opcional]
```

#### Tipos de Commit

- `feat`: Nueva funcionalidad
- `fix`: Corrección de bug
- `docs`: Cambios en documentación
- `style`: Cambios de formato (espacios, comas, etc.)
- `refactor`: Refactorización de código
- `test`: Agregar o modificar tests
- `chore`: Tareas de mantenimiento

#### Ejemplos

```bash
# Nueva funcionalidad
git commit -m "feat(auth): agregar autenticación con JWT"

# Corrección de bug
git commit -m "fix(api): corregir error en paginación de usuarios"

# Documentación
git commit -m "docs: actualizar guía de instalación"

# Refactorización
git commit -m "refactor(services): simplificar lógica de UserService"
```

### Branching Strategy

```
main
├── develop
│   ├── feature/nueva-funcionalidad
│   ├── feature/otra-funcionalidad
│   └── fix/correccion-bug
├── release/v1.1.0
└── hotfix/correccion-critica
```

#### Tipos de Branches

- **main**: Código en producción
- **develop**: Código en desarrollo
- **feature/**: Nuevas funcionalidades
- **fix/**: Correcciones de bugs
- **release/**: Preparación de releases
- **hotfix/**: Correcciones críticas

## Proceso de Review

### Checklist para Pull Requests

#### Código
- [ ] El código sigue las convenciones del proyecto
- [ ] No hay código comentado o debug statements
- [ ] Las funciones tienen documentación JSDoc
- [ ] Se manejan adecuadamente los errores
- [ ] No hay hardcoded values (usar configuración)

#### Testing
- [ ] Se agregaron tests para nueva funcionalidad
- [ ] Todos los tests pasan
- [ ] La cobertura de tests se mantiene o mejora
- [ ] Se probó manualmente la funcionalidad

#### Documentación
- [ ] Se actualizó la documentación relevante
- [ ] Se agregaron comentarios para código complejo
- [ ] Se actualizó el CHANGELOG si es necesario

#### Performance
- [ ] No hay consultas N+1
- [ ] Se consideró el impacto en performance
- [ ] Se optimizaron consultas de base de datos

### Proceso de Revisión

1. **Auto-revisión**: Revisar tu propio código antes de crear PR
2. **Automated Checks**: CI/CD ejecuta tests y linting
3. **Peer Review**: Al menos un reviewer debe aprobar
4. **Testing**: QA testing si es necesario
5. **Merge**: Merge después de todas las aprobaciones

## Configuración de Herramientas

### ESLint

```javascript
// .eslintrc.js
module.exports = {
  env: {
    node: true,
    es2021: true,
    jest: true
  },
  extends: [
    'eslint:recommended',
    '@strapi/eslint-config/back-end'
  ],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module'
  },
  rules: {
    'no-console': 'warn',
    'no-unused-vars': 'error',
    'prefer-const': 'error'
  }
};
```

### Prettier

```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'es5',
  printWidth: 80,
  bracketSpacing: true,
  arrowParens: 'avoid'
};
```

### Husky (Git Hooks)

```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.js": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ],
    "*.md": [
      "prettier --write",
      "git add"
    ]
  }
}
```

## Recursos para Contribuidores

### Documentación Técnica

- [Arquitectura del Sistema](../database/architecture.md)
- [Sistema de Seeds](../database/seeds-migrations.md)
- [Guía de Setup](./setup.md)
- [API Reference](../api/reference.md)

### Herramientas Recomendadas

- **Editor**: VS Code con extensiones recomendadas
- **Git Client**: GitKraken, SourceTree, o línea de comandos
- **API Testing**: Postman o Insomnia
- **Database**: DB Browser for SQLite, pgAdmin para PostgreSQL

### Comunidad

- **Discord**: [Enlace al servidor de Discord]
- **Slack**: [Enlace al workspace de Slack]
- **Reuniones**: Reuniones semanales los viernes a las 2 PM

## Reconocimientos

### Tipos de Contribución

- 🐛 **Bug Reports**: Reportar y ayudar a reproducir bugs
- 💻 **Code**: Contribuciones de código
- 📖 **Documentation**: Mejorar o crear documentación
- 🎨 **Design**: Diseño de UI/UX
- 💡 **Ideas**: Sugerencias de funcionalidades
- 🤔 **Mentoring**: Ayudar a otros contribuidores
- 📢 **Outreach**: Promocionar el proyecto
- 🔧 **Tools**: Herramientas y scripts de utilidad
- 🧪 **Tests**: Escribir y mejorar tests

### Hall of Fame

<!-- Aquí se listarán los contribuidores principales -->

## Preguntas Frecuentes

### ¿Cómo puedo empezar a contribuir?

1. Lee esta guía completamente
2. Configura tu entorno de desarrollo
3. Busca issues etiquetados como "good first issue"
4. Únete a nuestros canales de comunicación

### ¿Qué pasa si mi PR es rechazado?

No te desanimes. El feedback es una oportunidad de aprendizaje. Revisa los comentarios, haz los cambios necesarios y vuelve a enviar.

### ¿Puedo trabajar en múltiples issues al mismo tiempo?

Recomendamos enfocarse en un issue a la vez, especialmente si eres nuevo en el proyecto.

### ¿Cómo reporto un problema de seguridad?

Para problemas de seguridad, envía un email privado a [<EMAIL>] en lugar de crear un issue público.

## Contacto

Si tienes preguntas sobre cómo contribuir, no dudes en contactarnos:

- **Email**: [<EMAIL>]
- **Discord**: [Enlace al servidor]
- **Issues**: [Enlace a GitHub Issues]

---

¡Gracias por contribuir al proyecto SCMP! Tu ayuda es invaluable para servir mejor a la comunidad de la iglesia.