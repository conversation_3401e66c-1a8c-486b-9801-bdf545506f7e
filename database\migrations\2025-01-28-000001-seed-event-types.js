/**
 * Migración para sembrar los tipos de eventos
 * Esta migración crea los tipos de eventos predefinidos con sus configuraciones por defecto
 */

const eventTypesData = [
  { id: 1,  name: '<PERSON><PERSON><PERSON>',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 2,  name: 'Congreso',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 3,  name: 'Encuentro',              autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 4,  name: 'Instrucción',            autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 5,  name: 'Entrenamiento',          autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 6,  name: '<PERSON>urs<PERSON>',                  autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 7,  name: 'Taller',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 8,  name: '<PERSON><PERSON><PERSON>',                autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 9,  name: 'Capacitación',           autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 10, name: 'Certificación',          autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 11, name: 'Escuela',                autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 12, name: 'INCALA',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 13, name: 'Campamento',             autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 14, name: 'Retiro',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 15, name: 'Cumbre',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 16, name: 'Camporee',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 17, name: 'Seminario',              autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 18, name: 'Seminario Taller',       autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 19, name: 'Convención',             autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 20, name: 'Reunión',                autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 21, name: 'Reunión de Instrucción', autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 22, name: 'Festival',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' }
];

/**
 * Ejecutar la migración - sembrar tipos de eventos
 */
async function up(knex) {
    console.log('🌱 Iniciando siembra de tipos de eventos...');
    
    try {
      // Verificar si la tabla existe
      const tableExists = await knex.schema.hasTable('event_types');
      
      if (!tableExists) {
        console.log('⚠️  La tabla event_types no existe aún. Saltando siembra.');
        return;
      }
      
      // Verificar si ya existen tipos de eventos
      const existingEventTypes = await knex('event_types').select('*');
      
      if (existingEventTypes.length > 0) {
        console.log('⚠️  Los tipos de eventos ya existen. Saltando siembra.');
        return;
      }
      
      // Crear cada tipo de evento
      for (const eventTypeData of eventTypesData) {
        await knex('event_types').insert({
          name: eventTypeData.name,
          auto_registration_default: eventTypeData.autoRegistrationDefault,
          attendance_method_default: eventTypeData.attendanceMethodDefault,
          description: `Tipo de evento: ${eventTypeData.name}`,
          created_at: new Date(),
          updated_at: new Date()
        });
        
        console.log(`✅ Tipo de evento creado: ${eventTypeData.name}`);
      }
      
      console.log('🎉 Siembra de tipos de eventos completada exitosamente!');
      
    } catch (error) {
      console.error('❌ Error durante la siembra de tipos de eventos:', error);
      throw error;
    }
}

module.exports = { up };