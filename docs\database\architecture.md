# 🏗️ Arquitectura de la Base de Datos - SCMP

## Introducción

Este documento describe la arquitectura de la base de datos del Sistema de Competencia y Ministerio Pastoral (SCMP), construido sobre Strapi v5.

## Modelo de Datos

### Diagrama de Entidad-Relación

```mermaid
erDiagram
    FIELD {
        int id PK
        string documentId UK
        string name
        string acronym
        string type
        datetime publishedAt
        string locale
        datetime createdAt
        datetime updatedAt
    }
    
    ZONE {
        int id PK
        string documentId UK
        string name
        datetime publishedAt
        string locale
        datetime createdAt
        datetime updatedAt
    }
    
    DISTRICT {
        int id PK
        string documentId UK
        string name
        datetime publishedAt
        string locale
        datetime createdAt
        datetime updatedAt
    }
    
    CHURCH {
        int id PK
        string documentId UK
        string name
        string address
        string phone
        string email
        datetime publishedAt
        string locale
        datetime createdAt
        datetime updatedAt
    }
    
    ZONES_FIELD_LNK {
        int id PK
        int zone_id FK
        int field_id FK
    }
    
    DISTRICTS_ZONE_LNK {
        int id PK
        int district_id FK
        int zone_id FK
    }
    
    CHURCHES_DISTRICT_LNK {
        int id PK
        int church_id FK
        int district_id FK
    }
    
    FIELD ||--o{ ZONES_FIELD_LNK : "has"
    ZONE ||--o{ ZONES_FIELD_LNK : "belongs to"
    ZONE ||--o{ DISTRICTS_ZONE_LNK : "has"
    DISTRICT ||--o{ DISTRICTS_ZONE_LNK : "belongs to"
    DISTRICT ||--o{ CHURCHES_DISTRICT_LNK : "has"
    CHURCH ||--o{ CHURCHES_DISTRICT_LNK : "belongs to"
```

## Entidades Principales

### 1. Field (Campo/Asociación)

**Propósito**: Representa las asociaciones o campos regionales de la iglesia.

**Atributos**:
- `id`: Identificador único interno
- `documentId`: Identificador único público (UUID)
- `name`: Nombre completo del campo
- `acronym`: Acrónimo o siglas
- `type`: Tipo de campo (ej: "association")
- `publishedAt`: Fecha de publicación
- `locale`: Idioma/localización
- `createdAt`: Fecha de creación
- `updatedAt`: Fecha de última actualización

**Ejemplo**:
```json
{
  "documentId": "acd-001",
  "name": "Asociación Central Dominicana",
  "acronym": "ACD",
  "type": "association"
}
```

### 2. Zone (Zona)

**Propósito**: Subdivisiones administrativas dentro de un campo.

**Atributos**:
- `id`: Identificador único interno
- `documentId`: Identificador único público (UUID)
- `name`: Nombre de la zona
- `publishedAt`: Fecha de publicación
- `locale`: Idioma/localización
- `createdAt`: Fecha de creación
- `updatedAt`: Fecha de última actualización

**Relaciones**:
- Pertenece a un `Field` (manyToOne)
- Tiene múltiples `Districts` (oneToMany)

### 3. District (Distrito)

**Propósito**: Subdivisiones administrativas dentro de una zona.

**Atributos**:
- `id`: Identificador único interno
- `documentId`: Identificador único público (UUID)
- `name`: Nombre del distrito
- `publishedAt`: Fecha de publicación
- `locale`: Idioma/localización
- `createdAt`: Fecha de creación
- `updatedAt`: Fecha de última actualización

**Relaciones**:
- Pertenece a una `Zone` (manyToOne)
- Tiene múltiples `Churches` (oneToMany)

### 4. Church (Iglesia)

**Propósito**: Iglesias locales individuales.

**Atributos**:
- `id`: Identificador único interno
- `documentId`: Identificador único público (UUID)
- `name`: Nombre de la iglesia
- `address`: Dirección física
- `phone`: Número de teléfono
- `email`: Correo electrónico
- `publishedAt`: Fecha de publicación
- `locale`: Idioma/localización
- `createdAt`: Fecha de creación
- `updatedAt`: Fecha de última actualización

**Relaciones**:
- Pertenece a un `District` (manyToOne)

## Tablas de Enlace

### Concepto

Strapi v5 utiliza tablas de enlace para manejar relaciones entre entidades, en lugar de claves foráneas directas en las tablas principales.

### 1. zones_field_lnk

**Propósito**: Relaciona zonas con campos.

**Estructura**:
```sql
CREATE TABLE zones_field_lnk (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  zone_id INTEGER NOT NULL,
  field_id INTEGER NOT NULL,
  FOREIGN KEY (zone_id) REFERENCES zones(id) ON DELETE CASCADE,
  FOREIGN KEY (field_id) REFERENCES fields(id) ON DELETE CASCADE
);
```

### 2. districts_zone_lnk

**Propósito**: Relaciona distritos con zonas.

**Estructura**:
```sql
CREATE TABLE districts_zone_lnk (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  district_id INTEGER NOT NULL,
  zone_id INTEGER NOT NULL,
  FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE,
  FOREIGN KEY (zone_id) REFERENCES zones(id) ON DELETE CASCADE
);
```

### 3. churches_district_lnk

**Propósito**: Relaciona iglesias con distritos.

**Estructura**:
```sql
CREATE TABLE churches_district_lnk (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  church_id INTEGER NOT NULL,
  district_id INTEGER NOT NULL,
  FOREIGN KEY (church_id) REFERENCES churches(id) ON DELETE CASCADE,
  FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE
);
```

## Esquemas de Strapi

### Field Schema

```javascript
// src/api/field/content-types/field/schema.json
{
  "kind": "collectionType",
  "collectionName": "fields",
  "info": {
    "singularName": "field",
    "pluralName": "fields",
    "displayName": "Field"
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true
    },
    "acronym": {
      "type": "string"
    },
    "type": {
      "type": "string"
    },
    "zones": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::zone.zone",
      "mappedBy": "field"
    }
  }
}
```

### Zone Schema

```javascript
// src/api/zone/content-types/zone/schema.json
{
  "kind": "collectionType",
  "collectionName": "zones",
  "info": {
    "singularName": "zone",
    "pluralName": "zones",
    "displayName": "Zone"
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true
    },
    "field": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::field.field",
      "inversedBy": "zones"
    },
    "districts": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::district.district",
      "mappedBy": "zone"
    }
  }
}
```

## Índices y Optimización

### Índices Recomendados

```sql
-- Índices para búsquedas frecuentes
CREATE INDEX idx_zones_field_lnk_field_id ON zones_field_lnk(field_id);
CREATE INDEX idx_zones_field_lnk_zone_id ON zones_field_lnk(zone_id);

CREATE INDEX idx_districts_zone_lnk_zone_id ON districts_zone_lnk(zone_id);
CREATE INDEX idx_districts_zone_lnk_district_id ON districts_zone_lnk(district_id);

CREATE INDEX idx_churches_district_lnk_district_id ON churches_district_lnk(district_id);
CREATE INDEX idx_churches_district_lnk_church_id ON churches_district_lnk(church_id);

-- Índices para documentId (búsquedas por UUID)
CREATE UNIQUE INDEX idx_fields_document_id ON fields(documentId);
CREATE UNIQUE INDEX idx_zones_document_id ON zones(documentId);
CREATE UNIQUE INDEX idx_districts_document_id ON districts(documentId);
CREATE UNIQUE INDEX idx_churches_document_id ON churches(documentId);

-- Índices para nombres (búsquedas de texto)
CREATE INDEX idx_fields_name ON fields(name);
CREATE INDEX idx_zones_name ON zones(name);
CREATE INDEX idx_districts_name ON districts(name);
CREATE INDEX idx_churches_name ON churches(name);
```

### Consideraciones de Performance

1. **Consultas Jerárquicas**: Las consultas que atraviesan múltiples niveles de la jerarquía pueden ser costosas
2. **Paginación**: Implementar paginación para listas grandes
3. **Caché**: Considerar caché para consultas frecuentes
4. **Agregaciones**: Usar vistas materializadas para estadísticas

## Consultas Comunes

### 1. Obtener todas las iglesias de un campo

```sql
SELECT c.*
FROM churches c
JOIN churches_district_lnk cdl ON c.id = cdl.church_id
JOIN districts d ON cdl.district_id = d.id
JOIN districts_zone_lnk dzl ON d.id = dzl.district_id
JOIN zones z ON dzl.zone_id = z.id
JOIN zones_field_lnk zfl ON z.id = zfl.zone_id
JOIN fields f ON zfl.field_id = f.id
WHERE f.documentId = 'acd-001';
```

### 2. Contar iglesias por zona

```sql
SELECT z.name as zone_name, COUNT(c.id) as church_count
FROM zones z
LEFT JOIN districts_zone_lnk dzl ON z.id = dzl.zone_id
LEFT JOIN districts d ON dzl.district_id = d.id
LEFT JOIN churches_district_lnk cdl ON d.id = cdl.district_id
LEFT JOIN churches c ON cdl.church_id = c.id
GROUP BY z.id, z.name
ORDER BY church_count DESC;
```

### 3. Jerarquía completa de una iglesia

```sql
SELECT 
  f.name as field_name,
  z.name as zone_name,
  d.name as district_name,
  c.name as church_name
FROM churches c
JOIN churches_district_lnk cdl ON c.id = cdl.church_id
JOIN districts d ON cdl.district_id = d.id
JOIN districts_zone_lnk dzl ON d.id = dzl.district_id
JOIN zones z ON dzl.zone_id = z.id
JOIN zones_field_lnk zfl ON z.id = zfl.zone_id
JOIN fields f ON zfl.field_id = f.id
WHERE c.documentId = 'church-uuid';
```

## Migración y Versionado

### Estrategia de Migración

1. **Migraciones Incrementales**: Cada cambio en una migración separada
2. **Rollback**: Siempre incluir función `down()` para rollback
3. **Testing**: Probar migraciones en entorno de desarrollo
4. **Backup**: Backup antes de migraciones en producción

### Versionado de Esquema

```javascript
// Ejemplo de migración de esquema
module.exports = {
  async up(knex) {
    // Agregar nueva columna
    await knex.schema.table('churches', (table) => {
      table.string('website').nullable();
    });
  },
  
  async down(knex) {
    // Remover columna
    await knex.schema.table('churches', (table) => {
      table.dropColumn('website');
    });
  }
};
```

## Seguridad

### Consideraciones de Seguridad

1. **Validación de Entrada**: Validar todos los datos de entrada
2. **Sanitización**: Sanitizar datos antes de almacenar
3. **Permisos**: Implementar control de acceso basado en roles
4. **Auditoría**: Registrar cambios importantes

### Backup y Recuperación

1. **Backup Automático**: Configurar backups automáticos
2. **Punto de Recuperación**: Definir RPO y RTO
3. **Testing de Recuperación**: Probar procedimientos regularmente
4. **Documentación**: Documentar procedimientos de recuperación

## Monitoreo

### Métricas Clave

- **Tiempo de Respuesta**: Consultas lentas
- **Uso de Memoria**: Consumo de memoria de la base de datos
- **Conexiones**: Número de conexiones activas
- **Errores**: Errores de base de datos

### Alertas

- Consultas que toman más de 5 segundos
- Uso de memoria superior al 80%
- Errores de conexión
- Espacio en disco bajo

## Referencias

- [Strapi v5 Database Documentation](https://docs.strapi.io/dev-docs/database)
- [Knex.js Schema Builder](http://knexjs.org/#Schema)
- [SQLite Documentation](https://www.sqlite.org/docs.html)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## Changelog

### v1.0.0 (2025-01-28)
- ✅ Diseño inicial de la arquitectura
- ✅ Implementación de entidades principales
- ✅ Sistema de relaciones con tablas de enlace
- ✅ Índices de optimización
- ✅ Documentación completa