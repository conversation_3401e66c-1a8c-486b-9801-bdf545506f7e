/**
 * Migración de Strapi v5 para corregir documentId faltantes en departamentos
 * Fecha: 2025-01-28
 * Descripción: Genera documentId únicos para todos los departamentos existentes que no lo tienen
 */

const { v4: uuidv4 } = require('uuid');

/**
 * Función para generar un documentId único en formato Strapi v5
 * Strapi v5 usa un formato específico para documentId
 */
function generateDocumentId() {
  // Generar UUID v4 y remover guiones para formato Strapi
  return uuidv4().replace(/-/g, '');
}

/**
 * Función para verificar si un documentId ya existe
 */
async function documentIdExists(knex, documentId, tableName = 'departments') {
  try {
    const existing = await knex(tableName)
      .where({ document_id: documentId })
      .first();
    return !!existing;
  } catch (error) {
    console.error(`Error al verificar documentId ${documentId}:`, error.message);
    return false;
  }
}

/**
 * Función para generar un documentId único que no exista en la tabla
 */
async function generateUniqueDocumentId(knex, tableName = 'departments') {
  let documentId;
  let attempts = 0;
  const maxAttempts = 10;
  
  do {
    documentId = generateDocumentId();
    attempts++;
    
    if (attempts > maxAttempts) {
      throw new Error(`No se pudo generar un documentId único después de ${maxAttempts} intentos`);
    }
  } while (await documentIdExists(knex, documentId, tableName));
  
  return documentId;
}

module.exports = {
  /**
   * Migración hacia adelante (up)
   */
  async up(knex) {
    console.log('🚀 Iniciando corrección de documentId para departamentos...');
    
    try {
      // Verificar si la tabla existe
      const tableExists = await knex.schema.hasTable('departments');
      
      if (!tableExists) {
        console.log('⚠️  La tabla departments no existe. Saltando migración.');
        return;
      }
      
      // Verificar si la columna document_id existe
      const hasDocumentIdColumn = await knex.schema.hasColumn('departments', 'document_id');
      
      if (!hasDocumentIdColumn) {
        console.log('⚠️  La columna document_id no existe. Saltando migración.');
        console.log('💡 Ejecute primero las migraciones de Strapi para crear la columna.');
        return;
      }
      
      // Obtener todos los departamentos sin documentId
      const departmentsWithoutDocumentId = await knex('departments')
        .whereNull('document_id')
        .orWhere('document_id', '')
        .select(['id', 'name']);
      
      console.log(`📋 Departamentos sin documentId encontrados: ${departmentsWithoutDocumentId.length}`);
      
      if (departmentsWithoutDocumentId.length === 0) {
        console.log('✅ Todos los departamentos ya tienen documentId. No se requiere acción.');
        return;
      }
      
      let updatedCount = 0;
      let errorCount = 0;
      
      // Procesar cada departamento sin documentId
      for (const department of departmentsWithoutDocumentId) {
        try {
          console.log(`\n🔧 Procesando: ${department.name} (ID: ${department.id})`);
          
          // Generar documentId único
          const documentId = await generateUniqueDocumentId(knex, 'departments');
          
          // Actualizar el departamento con el nuevo documentId
          const updateResult = await knex('departments')
            .where({ id: department.id })
            .update({
              document_id: documentId,
              updated_at: new Date()
            });
          
          if (updateResult > 0) {
            console.log(`✅ DocumentId asignado: ${documentId}`);
            updatedCount++;
          } else {
            console.log(`⚠️  No se pudo actualizar el departamento ${department.name}`);
            errorCount++;
          }
          
        } catch (error) {
          console.error(`❌ Error procesando ${department.name}:`, error.message);
          errorCount++;
        }
      }
      
      // Resumen
      console.log('\n📊 RESUMEN DE CORRECCIÓN:');
      console.log(`✅ Departamentos actualizados: ${updatedCount}`);
      console.log(`❌ Errores encontrados: ${errorCount}`);
      console.log(`📋 Total procesados: ${departmentsWithoutDocumentId.length}`);
      
      if (errorCount === 0) {
        console.log('🎉 Corrección de documentId completada exitosamente!');
      } else {
        console.log('⚠️  Corrección completada con algunos errores.');
      }
      
      // Verificación final
      const remainingWithoutDocumentId = await knex('departments')
        .whereNull('document_id')
        .orWhere('document_id', '')
        .count('id as count');
      
      const remaining = parseInt(remainingWithoutDocumentId[0].count);
      console.log(`\n🔍 Verificación final: ${remaining} departamentos sin documentId`);
      
    } catch (error) {
      console.error('❌ Error en migración de corrección de documentId:', error.message);
      throw error;
    }
  },

  /**
   * Migración hacia atrás (down)
   * ADVERTENCIA: Esta operación eliminará todos los documentId generados
   */
  async down(knex) {
    console.log('🔄 Revirtiendo corrección de documentId...');
    console.log('⚠️  ADVERTENCIA: Esta operación eliminará todos los documentId generados por esta migración.');
    
    try {
      // Verificar si la tabla existe
      const tableExists = await knex.schema.hasTable('departments');
      
      if (!tableExists) {
        console.log('⚠️  La tabla departments no existe. No se requiere acción.');
        return;
      }
      
      // Verificar si la columna document_id existe
      const hasDocumentIdColumn = await knex.schema.hasColumn('departments', 'document_id');
      
      if (!hasDocumentIdColumn) {
        console.log('⚠️  La columna document_id no existe. No se requiere acción.');
        return;
      }
      
      // Contar departamentos con documentId
      const departmentsWithDocumentId = await knex('departments')
        .whereNotNull('document_id')
        .andWhere('document_id', '!=', '')
        .count('id as count');
      
      const count = parseInt(departmentsWithDocumentId[0].count);
      console.log(`📋 Departamentos con documentId encontrados: ${count}`);
      
      if (count === 0) {
        console.log('✅ No hay documentId para eliminar.');
        return;
      }
      
      // Eliminar todos los documentId (establecer como NULL)
      const updateResult = await knex('departments')
        .whereNotNull('document_id')
        .andWhere('document_id', '!=', '')
        .update({
          document_id: null,
          updated_at: new Date()
        });
      
      console.log(`🗑️  DocumentId eliminados de ${updateResult} departamentos`);
      console.log('✅ Reversión completada');
      
    } catch (error) {
      console.error('❌ Error en reversión de migración:', error.message);
      throw error;
    }
  }
};