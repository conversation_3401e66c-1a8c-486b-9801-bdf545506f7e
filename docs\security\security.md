# 🔒 Guía de Seguridad - SCMP

## Introducción

Esta guía establece las políticas, procedimientos y mejores prácticas de seguridad para el Sistema de Competencia y Ministerio Pastoral (SCMP). La seguridad es fundamental para proteger la información sensible de los miembros y mantener la integridad del sistema.

## Principios de Seguridad

### 1. Defensa en Profundidad
- Múltiples capas de seguridad
- Principio de menor privilegio
- Segregación de responsabilidades
- Monitoreo continuo

### 2. Confidencialidad, Integridad y Disponibilidad (CIA)
- **Confidencialidad**: Protección de datos sensibles
- **Integridad**: Prevención de modificaciones no autorizadas
- **Disponibilidad**: Garantía de acceso cuando sea necesario

### 3. Cumplimiento y Privacidad
- Protección de datos personales
- Cumplimiento con regulaciones locales
- Transparencia en el manejo de datos
- Derecho al olvido

## Arquitectura de Seguridad

### 1. Modelo de Seguridad por Capas

```
┌─────────────────────────────────────┐
│           Usuario Final             │
├─────────────────────────────────────┤
│        Autenticación/Autorización   │
├─────────────────────────────────────┤
│           API Gateway               │
├─────────────────────────────────────┤
│         Aplicación Strapi           │
├─────────────────────────────────────┤
│          Base de Datos              │
├─────────────────────────────────────┤
│        Infraestructura/Red          │
└─────────────────────────────────────┘
```

### 2. Componentes de Seguridad

#### Frontend
- Validación de entrada
- Sanitización de datos
- Protección CSRF
- Content Security Policy (CSP)

#### API
- Autenticación JWT
- Rate limiting
- Validación de esquemas
- Logging de seguridad

#### Base de Datos
- Encriptación en reposo
- Conexiones seguras (SSL/TLS)
- Backup encriptado
- Auditoría de accesos

#### Infraestructura
- Firewall
- Monitoreo de intrusiones
- Actualizaciones de seguridad
- Gestión de certificados

## Autenticación y Autorización

### 1. Estrategia de Autenticación

#### JWT (JSON Web Tokens)

```javascript
// config/plugins.js
module.exports = {
  'users-permissions': {
    config: {
      jwt: {
        expiresIn: '7d',
        algorithm: 'HS256'
      },
      ratelimit: {
        max: 5,
        duration: 60000, // 1 minuto
        message: 'Demasiados intentos de login'
      }
    }
  }
};
```

#### Configuración de Seguridad JWT

```javascript
// config/middlewares.js
module.exports = [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': ["'self'", 'data:', 'blob:', 'https:'],
          'media-src': ["'self'", 'data:', 'blob:'],
          upgradeInsecureRequests: null,
        },
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      },
      frameguard: {
        action: 'deny'
      },
      noSniff: true,
      xssFilter: true
    },
  },
  'strapi::cors',
  'strapi::poweredBy',
  'strapi::logger',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
```

### 2. Roles y Permisos

#### Estructura de Roles

```javascript
// Roles del sistema
const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  PASTOR: 'pastor',
  LEADER: 'leader',
  MEMBER: 'member',
  VISITOR: 'visitor'
};

// Permisos por rol
const PERMISSIONS = {
  [ROLES.SUPER_ADMIN]: {
    fields: ['create', 'read', 'update', 'delete'],
    zones: ['create', 'read', 'update', 'delete'],
    districts: ['create', 'read', 'update', 'delete'],
    churches: ['create', 'read', 'update', 'delete'],
    members: ['create', 'read', 'update', 'delete'],
    users: ['create', 'read', 'update', 'delete'],
    system: ['backup', 'restore', 'configure']
  },
  [ROLES.ADMIN]: {
    fields: ['read'],
    zones: ['read'],
    districts: ['create', 'read', 'update'],
    churches: ['create', 'read', 'update'],
    members: ['create', 'read', 'update'],
    users: ['create', 'read', 'update']
  },
  [ROLES.PASTOR]: {
    districts: ['read'],
    churches: ['read', 'update'], // Solo su iglesia
    members: ['create', 'read', 'update'] // Solo su iglesia
  },
  [ROLES.LEADER]: {
    churches: ['read'], // Solo su iglesia
    members: ['read', 'update'] // Solo su iglesia
  },
  [ROLES.MEMBER]: {
    churches: ['read'], // Solo su iglesia
    members: ['read'] // Solo información básica
  },
  [ROLES.VISITOR]: {
    churches: ['read'] // Solo información pública
  }
};
```

#### Middleware de Autorización

```javascript
// src/middlewares/rbac.js
module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    const { user } = ctx.state;
    const { method, path } = ctx.request;
    
    if (!user) {
      return ctx.unauthorized('Token de autenticación requerido');
    }
    
    const userRole = user.role?.name;
    const requiredPermission = getRequiredPermission(method, path);
    
    if (!hasPermission(userRole, requiredPermission)) {
      return ctx.forbidden('Permisos insuficientes');
    }
    
    // Aplicar filtros basados en rol
    applyRoleBasedFilters(ctx, userRole);
    
    await next();
  };
};

function getRequiredPermission(method, path) {
  const methodMap = {
    'GET': 'read',
    'POST': 'create',
    'PUT': 'update',
    'DELETE': 'delete'
  };
  
  const resource = extractResourceFromPath(path);
  return { resource, action: methodMap[method] };
}

function hasPermission(role, permission) {
  const rolePermissions = PERMISSIONS[role];
  if (!rolePermissions) return false;
  
  const resourcePermissions = rolePermissions[permission.resource];
  return resourcePermissions?.includes(permission.action);
}

function applyRoleBasedFilters(ctx, role) {
  const { user } = ctx.state;
  
  switch (role) {
    case ROLES.PASTOR:
      // Filtrar solo su iglesia
      ctx.query.filters = {
        ...ctx.query.filters,
        church: { id: user.church?.id }
      };
      break;
      
    case ROLES.LEADER:
    case ROLES.MEMBER:
      // Filtrar solo su iglesia y datos públicos
      ctx.query.filters = {
        ...ctx.query.filters,
        church: { id: user.church?.id },
        isPublic: true
      };
      break;
      
    case ROLES.VISITOR:
      // Solo datos públicos
      ctx.query.filters = {
        ...ctx.query.filters,
        isPublic: true
      };
      break;
  }
}
```

### 3. Autenticación Multifactor (MFA)

#### Configuración de TOTP

```javascript
// src/extensions/users-permissions/services/user.js
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');

module.exports = {
  async generateMFASecret(userId) {
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId
    );
    
    const secret = speakeasy.generateSecret({
      name: `SCMP (${user.email})`,
      issuer: 'SCMP Church Management'
    });
    
    // Guardar secret temporal
    await strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      {
        data: {
          mfaSecretTemp: secret.base32
        }
      }
    );
    
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);
    
    return {
      secret: secret.base32,
      qrCode: qrCodeUrl
    };
  },
  
  async enableMFA(userId, token) {
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId
    );
    
    const verified = speakeasy.totp.verify({
      secret: user.mfaSecretTemp,
      encoding: 'base32',
      token,
      window: 2
    });
    
    if (!verified) {
      throw new Error('Token MFA inválido');
    }
    
    await strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      {
        data: {
          mfaSecret: user.mfaSecretTemp,
          mfaSecretTemp: null,
          mfaEnabled: true
        }
      }
    );
    
    return true;
  },
  
  async verifyMFA(userId, token) {
    const user = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId
    );
    
    if (!user.mfaEnabled) {
      return true; // MFA no habilitado
    }
    
    return speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token,
      window: 2
    });
  }
};
```

## Protección de Datos

### 1. Encriptación

#### Encriptación de Campos Sensibles

```javascript
// src/utils/encryption.js
const crypto = require('crypto');

const ALGORITHM = 'aes-256-gcm';
const SECRET_KEY = process.env.ENCRYPTION_KEY; // 32 bytes

class EncryptionService {
  static encrypt(text) {
    if (!text) return text;
    
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(ALGORITHM, SECRET_KEY);
    cipher.setAAD(Buffer.from('scmp-data'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }
  
  static decrypt(encryptedData) {
    if (!encryptedData || typeof encryptedData === 'string') {
      return encryptedData;
    }
    
    const { encrypted, iv, authTag } = encryptedData;
    
    const decipher = crypto.createDecipher(ALGORITHM, SECRET_KEY);
    decipher.setAAD(Buffer.from('scmp-data'));
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

module.exports = EncryptionService;
```

#### Lifecycle Hooks para Encriptación

```javascript
// src/api/member/content-types/member/lifecycles.js
const EncryptionService = require('../../../../utils/encryption');

module.exports = {
  async beforeCreate(event) {
    const { data } = event.params;
    
    // Encriptar campos sensibles
    if (data.phone) {
      data.phone = EncryptionService.encrypt(data.phone);
    }
    
    if (data.address) {
      data.address = EncryptionService.encrypt(data.address);
    }
    
    if (data.emergencyContact) {
      data.emergencyContact = EncryptionService.encrypt(data.emergencyContact);
    }
  },
  
  async beforeUpdate(event) {
    const { data } = event.params;
    
    // Encriptar campos sensibles si se están actualizando
    if (data.phone) {
      data.phone = EncryptionService.encrypt(data.phone);
    }
    
    if (data.address) {
      data.address = EncryptionService.encrypt(data.address);
    }
    
    if (data.emergencyContact) {
      data.emergencyContact = EncryptionService.encrypt(data.emergencyContact);
    }
  },
  
  async afterFindOne(event) {
    const { result } = event;
    
    if (result) {
      // Desencriptar para el usuario autorizado
      if (result.phone) {
        result.phone = EncryptionService.decrypt(result.phone);
      }
      
      if (result.address) {
        result.address = EncryptionService.decrypt(result.address);
      }
      
      if (result.emergencyContact) {
        result.emergencyContact = EncryptionService.decrypt(result.emergencyContact);
      }
    }
  },
  
  async afterFindMany(event) {
    const { result } = event;
    
    if (result?.data) {
      result.data.forEach(item => {
        if (item.phone) {
          item.phone = EncryptionService.decrypt(item.phone);
        }
        
        if (item.address) {
          item.address = EncryptionService.decrypt(item.address);
        }
        
        if (item.emergencyContact) {
          item.emergencyContact = EncryptionService.decrypt(item.emergencyContact);
        }
      });
    }
  }
};
```

### 2. Anonimización y Pseudonimización

```javascript
// src/utils/anonymization.js
const crypto = require('crypto');

class AnonymizationService {
  static anonymizeEmail(email) {
    const [local, domain] = email.split('@');
    const anonymizedLocal = local.charAt(0) + '*'.repeat(local.length - 2) + local.charAt(local.length - 1);
    return `${anonymizedLocal}@${domain}`;
  }
  
  static anonymizePhone(phone) {
    if (phone.length <= 4) return phone;
    return '*'.repeat(phone.length - 4) + phone.slice(-4);
  }
  
  static pseudonymize(data, salt) {
    return crypto.createHash('sha256')
      .update(data + salt)
      .digest('hex')
      .substring(0, 8);
  }
  
  static anonymizeMember(member) {
    return {
      id: member.id,
      firstName: this.pseudonymize(member.firstName, process.env.PSEUDO_SALT),
      lastName: this.pseudonymize(member.lastName, process.env.PSEUDO_SALT),
      email: this.anonymizeEmail(member.email),
      phone: this.anonymizePhone(member.phone),
      church: member.church,
      membershipDate: member.membershipDate,
      status: member.status
    };
  }
}

module.exports = AnonymizationService;
```

### 3. Gestión de Consentimiento

```javascript
// src/api/consent/content-types/consent/schema.json
{
  "kind": "collectionType",
  "collectionName": "consents",
  "info": {
    "singularName": "consent",
    "pluralName": "consents",
    "displayName": "Consent"
  },
  "options": {
    "draftAndPublish": false
  },
  "attributes": {
    "user": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "plugin::users-permissions.user"
    },
    "consentType": {
      "type": "enumeration",
      "enum": ["data_processing", "marketing", "analytics", "cookies"]
    },
    "granted": {
      "type": "boolean",
      "default": false
    },
    "grantedAt": {
      "type": "datetime"
    },
    "revokedAt": {
      "type": "datetime"
    },
    "ipAddress": {
      "type": "string"
    },
    "userAgent": {
      "type": "text"
    },
    "version": {
      "type": "string",
      "default": "1.0"
    }
  }
}
```

## Validación y Sanitización

### 1. Validación de Entrada

```javascript
// src/middlewares/validation.js
const Joi = require('joi');

const schemas = {
  member: Joi.object({
    firstName: Joi.string().min(2).max(50).pattern(/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/).required(),
    lastName: Joi.string().min(2).max(50).pattern(/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^[+]?[0-9\s\-\(\)]+$/).min(10).max(20),
    birthDate: Joi.date().max('now').iso(),
    address: Joi.string().max(200),
    emergencyContact: Joi.string().max(100)
  }),
  
  church: Joi.object({
    name: Joi.string().min(3).max(100).required(),
    address: Joi.string().max(200),
    phone: Joi.string().pattern(/^[+]?[0-9\s\-\(\)]+$/),
    email: Joi.string().email(),
    pastor: Joi.string().min(2).max(100)
  })
};

module.exports = (schemaName) => {
  return async (ctx, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return ctx.badRequest('Schema de validación no encontrado');
    }
    
    try {
      const { error, value } = schema.validate(ctx.request.body.data, {
        abortEarly: false,
        stripUnknown: true
      });
      
      if (error) {
        const details = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }));
        
        return ctx.badRequest('Datos de entrada inválidos', { details });
      }
      
      ctx.request.body.data = value;
      await next();
    } catch (err) {
      return ctx.badRequest('Error de validación', { error: err.message });
    }
  };
};
```

### 2. Sanitización de Datos

```javascript
// src/utils/sanitizer.js
const DOMPurify = require('isomorphic-dompurify');
const validator = require('validator');

class SanitizerService {
  static sanitizeHtml(html) {
    if (!html) return html;
    
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: []
    });
  }
  
  static sanitizeString(str) {
    if (!str) return str;
    
    // Escapar caracteres especiales
    return validator.escape(str.trim());
  }
  
  static sanitizeEmail(email) {
    if (!email) return email;
    
    return validator.normalizeEmail(email.toLowerCase().trim());
  }
  
  static sanitizePhone(phone) {
    if (!phone) return phone;
    
    // Remover caracteres no numéricos excepto + al inicio
    return phone.replace(/[^+0-9]/g, '').replace(/(?!^)\+/g, '');
  }
  
  static sanitizeObject(obj, rules) {
    const sanitized = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const rule = rules[key];
      
      if (!rule) {
        continue; // Ignorar campos no definidos
      }
      
      switch (rule.type) {
        case 'string':
          sanitized[key] = this.sanitizeString(value);
          break;
        case 'html':
          sanitized[key] = this.sanitizeHtml(value);
          break;
        case 'email':
          sanitized[key] = this.sanitizeEmail(value);
          break;
        case 'phone':
          sanitized[key] = this.sanitizePhone(value);
          break;
        default:
          sanitized[key] = value;
      }
    }
    
    return sanitized;
  }
}

module.exports = SanitizerService;
```

## Rate Limiting y Protección DDoS

### 1. Rate Limiting

```javascript
// src/middlewares/rate-limit.js
const rateLimit = require('koa2-ratelimit').RateLimit;
const Redis = require('ioredis');

const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD
});

const rateLimitConfigs = {
  // API general
  api: {
    interval: { min: 15 }, // 15 minutos
    max: 100, // 100 requests
    message: 'Demasiadas solicitudes, intenta más tarde'
  },
  
  // Login
  auth: {
    interval: { min: 15 },
    max: 5, // 5 intentos de login
    message: 'Demasiados intentos de login, intenta más tarde'
  },
  
  // Registro
  register: {
    interval: { hour: 1 },
    max: 3, // 3 registros por hora
    message: 'Demasiados registros, intenta más tarde'
  },
  
  // Reset password
  resetPassword: {
    interval: { hour: 1 },
    max: 3,
    message: 'Demasiadas solicitudes de reset, intenta más tarde'
  }
};

function createRateLimit(configName) {
  const config = rateLimitConfigs[configName];
  
  return rateLimit({
    driver: 'redis',
    db: redis,
    duration: config.interval,
    max: config.max,
    id: (ctx) => {
      // Combinar IP y user ID si está autenticado
      const ip = ctx.request.ip;
      const userId = ctx.state.user?.id;
      return userId ? `${ip}:${userId}` : ip;
    },
    headers: {
      remaining: 'Rate-Limit-Remaining',
      reset: 'Rate-Limit-Reset',
      total: 'Rate-Limit-Total'
    },
    errorMessage: config.message,
    throw: true
  });
}

module.exports = {
  api: createRateLimit('api'),
  auth: createRateLimit('auth'),
  register: createRateLimit('register'),
  resetPassword: createRateLimit('resetPassword')
};
```

### 2. Protección DDoS

```javascript
// src/middlewares/ddos-protection.js
const slowDown = require('koa-slow-down');

const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutos
  delayAfter: 50, // Permitir 50 requests a velocidad normal
  delayMs: 500, // Agregar 500ms de delay por request adicional
  maxDelayMs: 20000, // Máximo delay de 20 segundos
  skipSuccessfulRequests: true,
  skipFailedRequests: false,
  keyGenerator: (ctx) => {
    return ctx.request.ip;
  },
  skip: (ctx) => {
    // No aplicar a rutas de salud
    return ctx.path === '/health';
  },
  onLimitReached: (ctx) => {
    strapi.log.warn(`Rate limit reached for IP: ${ctx.request.ip}`);
  }
});

module.exports = speedLimiter;
```

## Logging y Monitoreo de Seguridad

### 1. Security Logger

```javascript
// src/utils/security-logger.js
const winston = require('winston');
const path = require('path');

const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'scmp-security' },
  transports: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'security-error.log'),
      level: 'error'
    }),
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'security.log')
    })
  ]
});

class SecurityLogger {
  static logAuthAttempt(ctx, success, user = null) {
    const logData = {
      event: 'auth_attempt',
      success,
      ip: ctx.request.ip,
      userAgent: ctx.request.headers['user-agent'],
      timestamp: new Date().toISOString(),
      userId: user?.id,
      email: user?.email
    };
    
    if (success) {
      securityLogger.info('Authentication successful', logData);
    } else {
      securityLogger.warn('Authentication failed', logData);
    }
  }
  
  static logPermissionDenied(ctx, resource, action) {
    securityLogger.warn('Permission denied', {
      event: 'permission_denied',
      resource,
      action,
      userId: ctx.state.user?.id,
      ip: ctx.request.ip,
      userAgent: ctx.request.headers['user-agent'],
      timestamp: new Date().toISOString()
    });
  }
  
  static logSuspiciousActivity(ctx, activity, details = {}) {
    securityLogger.error('Suspicious activity detected', {
      event: 'suspicious_activity',
      activity,
      details,
      userId: ctx.state.user?.id,
      ip: ctx.request.ip,
      userAgent: ctx.request.headers['user-agent'],
      timestamp: new Date().toISOString()
    });
  }
  
  static logDataAccess(ctx, resource, action, recordId = null) {
    securityLogger.info('Data access', {
      event: 'data_access',
      resource,
      action,
      recordId,
      userId: ctx.state.user?.id,
      ip: ctx.request.ip,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = SecurityLogger;
```

### 2. Middleware de Auditoría

```javascript
// src/middlewares/audit.js
const SecurityLogger = require('../utils/security-logger');

module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    const startTime = Date.now();
    const { method, path } = ctx.request;
    
    try {
      await next();
      
      // Log successful requests
      const duration = Date.now() - startTime;
      
      if (shouldAudit(method, path)) {
        const resource = extractResource(path);
        const action = getActionFromMethod(method);
        
        SecurityLogger.logDataAccess(ctx, resource, action);
      }
      
      // Detectar actividad sospechosa
      if (duration > 10000) { // Requests que toman más de 10 segundos
        SecurityLogger.logSuspiciousActivity(ctx, 'slow_request', {
          duration,
          path,
          method
        });
      }
      
    } catch (error) {
      // Log failed requests
      SecurityLogger.logSuspiciousActivity(ctx, 'request_error', {
        error: error.message,
        path,
        method
      });
      
      throw error;
    }
  };
};

function shouldAudit(method, path) {
  // Auditar operaciones sensibles
  const sensitivePatterns = [
    /\/api\/members/,
    /\/api\/users/,
    /\/admin/,
    /\/auth/
  ];
  
  return sensitivePatterns.some(pattern => pattern.test(path));
}

function extractResource(path) {
  const match = path.match(/\/api\/(\w+)/);
  return match ? match[1] : 'unknown';
}

function getActionFromMethod(method) {
  const methodMap = {
    'GET': 'read',
    'POST': 'create',
    'PUT': 'update',
    'DELETE': 'delete'
  };
  
  return methodMap[method] || 'unknown';
}
```

## Gestión de Vulnerabilidades

### 1. Análisis de Dependencias

```bash
#!/bin/bash
# scripts/security-audit.sh

echo "🔍 Ejecutando auditoría de seguridad..."

# Audit de npm
echo "📦 Verificando dependencias de npm..."
npm audit --audit-level=moderate

if [ $? -ne 0 ]; then
  echo "⚠️  Vulnerabilidades encontradas en dependencias de npm"
  echo "💡 Ejecuta 'npm audit fix' para corregir automáticamente"
fi

# Verificar configuración de seguridad
echo "🔧 Verificando configuración de seguridad..."

# Verificar variables de entorno críticas
required_vars=("JWT_SECRET" "API_TOKEN_SALT" "ADMIN_JWT_SECRET" "ENCRYPTION_KEY")

for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "❌ Variable de entorno faltante: $var"
  else
    echo "✅ Variable de entorno configurada: $var"
  fi
done

# Verificar permisos de archivos
echo "📁 Verificando permisos de archivos..."

find . -name "*.env*" -exec ls -la {} \;
find . -name "config" -type d -exec ls -la {} \;

echo "✅ Auditoría de seguridad completada"
```

### 2. Configuración de Dependabot

```yaml
# .github/dependabot.yml
version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "security-team"
    assignees:
      - "lead-developer"
    commit-message:
      prefix: "security"
      include: "scope"
    labels:
      - "security"
      - "dependencies"
    
    # Solo actualizaciones de seguridad
    allow:
      - dependency-type: "all"
        update-type: "security"
    
    # Ignorar actualizaciones mayores para dependencias críticas
    ignore:
      - dependency-name: "strapi"
        update-types: ["version-update:semver-major"]
```

### 3. Escaneo de Código

```yaml
# .github/workflows/security-scan.yml
name: Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * 1' # Lunes a las 2 AM

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run npm audit
      run: npm audit --audit-level=moderate
    
    - name: Run ESLint security rules
      run: npx eslint . --ext .js,.ts --config .eslintrc.security.js
    
    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: auto
    
    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: semgrep.sarif
      if: always()
```

## Respuesta a Incidentes

### 1. Plan de Respuesta

#### Clasificación de Incidentes

| Nivel | Descripción | Tiempo de Respuesta | Escalación |
|-------|-------------|-------------------|------------|
| **Crítico** | Brecha de datos, sistema comprometido | 15 minutos | Inmediata |
| **Alto** | Intento de intrusión, vulnerabilidad crítica | 1 hora | 2 horas |
| **Medio** | Actividad sospechosa, vulnerabilidad media | 4 horas | 8 horas |
| **Bajo** | Anomalías menores, vulnerabilidad baja | 24 horas | 48 horas |

#### Procedimiento de Respuesta

1. **Detección y Análisis**
   - Identificar el tipo de incidente
   - Evaluar el impacto y alcance
   - Documentar evidencias iniciales

2. **Contención**
   - Aislar sistemas afectados
   - Prevenir propagación
   - Preservar evidencias

3. **Erradicación**
   - Eliminar la causa raíz
   - Aplicar parches de seguridad
   - Fortalecer controles

4. **Recuperación**
   - Restaurar servicios
   - Monitorear sistemas
   - Validar integridad

5. **Lecciones Aprendidas**
   - Documentar el incidente
   - Actualizar procedimientos
   - Mejorar controles

### 2. Playbooks de Respuesta

#### Brecha de Datos

```markdown
# Playbook: Brecha de Datos

## Respuesta Inmediata (0-15 minutos)

1. **Confirmar el incidente**
   - [ ] Verificar la autenticidad del reporte
   - [ ] Identificar sistemas afectados
   - [ ] Evaluar el tipo de datos comprometidos

2. **Notificar al equipo**
   - [ ] Alertar al equipo de seguridad
   - [ ] Notificar a la dirección
   - [ ] Activar el centro de comando

3. **Contención inicial**
   - [ ] Aislar sistemas comprometidos
   - [ ] Cambiar credenciales críticas
   - [ ] Activar modo de solo lectura si es necesario

## Respuesta a Corto Plazo (15 minutos - 4 horas)

4. **Investigación forense**
   - [ ] Preservar logs y evidencias
   - [ ] Identificar vector de ataque
   - [ ] Determinar alcance del compromiso

5. **Contención completa**
   - [ ] Cerrar todas las vías de acceso
   - [ ] Aplicar parches de emergencia
   - [ ] Implementar controles adicionales

6. **Evaluación de impacto**
   - [ ] Identificar datos específicos comprometidos
   - [ ] Evaluar número de registros afectados
   - [ ] Determinar obligaciones legales

## Respuesta a Largo Plazo (4+ horas)

7. **Notificaciones externas**
   - [ ] Notificar a autoridades si es requerido
   - [ ] Informar a usuarios afectados
   - [ ] Coordinar con socios externos

8. **Recuperación**
   - [ ] Restaurar sistemas desde backups limpios
   - [ ] Implementar monitoreo adicional
   - [ ] Validar integridad de datos

9. **Comunicación**
   - [ ] Preparar comunicado público
   - [ ] Actualizar a stakeholders
   - [ ] Documentar lecciones aprendidas
```

## Cumplimiento y Regulaciones

### 1. Protección de Datos Personales

#### Principios de Protección

1. **Licitud, Lealtad y Transparencia**
   - Informar claramente sobre el uso de datos
   - Obtener consentimiento explícito
   - Procesar datos de manera justa

2. **Limitación de la Finalidad**
   - Usar datos solo para fines declarados
   - No procesar para fines incompatibles
   - Documentar cambios de propósito

3. **Minimización de Datos**
   - Recopilar solo datos necesarios
   - Limitar acceso a datos relevantes
   - Eliminar datos innecesarios

4. **Exactitud**
   - Mantener datos actualizados
   - Corregir datos inexactos
   - Permitir actualización por usuarios

5. **Limitación del Plazo de Conservación**
   - Definir períodos de retención
   - Eliminar datos vencidos
   - Archivar datos históricos

6. **Integridad y Confidencialidad**
   - Proteger contra acceso no autorizado
   - Implementar medidas técnicas
   - Capacitar al personal

#### Derechos de los Titulares

```javascript
// src/api/data-subject-rights/controllers/data-subject-rights.js
module.exports = {
  // Derecho de acceso
  async getMyData(ctx) {
    const userId = ctx.state.user.id;
    
    const userData = await strapi.entityService.findOne(
      'plugin::users-permissions.user',
      userId,
      {
        populate: {
          member: true,
          consents: true,
          auditLogs: true
        }
      }
    );
    
    // Anonimizar datos de otros usuarios
    const anonymizedData = AnonymizationService.anonymizeUserData(userData);
    
    ctx.body = {
      data: anonymizedData,
      exportedAt: new Date().toISOString(),
      format: 'JSON'
    };
  },
  
  // Derecho de rectificación
  async updateMyData(ctx) {
    const userId = ctx.state.user.id;
    const { data } = ctx.request.body;
    
    // Validar que solo se actualicen campos permitidos
    const allowedFields = ['firstName', 'lastName', 'email', 'phone'];
    const updateData = {};
    
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        updateData[field] = data[field];
      }
    }
    
    const updatedUser = await strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      { data: updateData }
    );
    
    // Log de auditoría
    SecurityLogger.logDataAccess(ctx, 'user', 'update', userId);
    
    ctx.body = { data: updatedUser };
  },
  
  // Derecho de supresión
  async deleteMyData(ctx) {
    const userId = ctx.state.user.id;
    
    // Verificar si hay restricciones legales
    const canDelete = await this.checkDeletionRestrictions(userId);
    
    if (!canDelete.allowed) {
      return ctx.badRequest('No se puede eliminar la cuenta', {
        reasons: canDelete.reasons
      });
    }
    
    // Anonimizar en lugar de eliminar para mantener integridad referencial
    await this.anonymizeUserData(userId);
    
    // Log de auditoría
    SecurityLogger.logDataAccess(ctx, 'user', 'anonymize', userId);
    
    ctx.body = {
      message: 'Datos anonimizados exitosamente',
      anonymizedAt: new Date().toISOString()
    };
  },
  
  // Derecho de portabilidad
  async exportMyData(ctx) {
    const userId = ctx.state.user.id;
    const format = ctx.query.format || 'json';
    
    const userData = await this.gatherUserData(userId);
    
    let exportData;
    let contentType;
    
    switch (format.toLowerCase()) {
      case 'csv':
        exportData = this.convertToCSV(userData);
        contentType = 'text/csv';
        break;
      case 'xml':
        exportData = this.convertToXML(userData);
        contentType = 'application/xml';
        break;
      default:
        exportData = JSON.stringify(userData, null, 2);
        contentType = 'application/json';
    }
    
    ctx.set('Content-Type', contentType);
    ctx.set('Content-Disposition', `attachment; filename="user-data-${userId}.${format}"`);
    
    ctx.body = exportData;
  }
};
```

### 2. Políticas de Retención

```javascript
// src/utils/data-retention.js
class DataRetentionService {
  static retentionPolicies = {
    users: {
      active: '7 years', // Usuarios activos
      inactive: '3 years', // Usuarios inactivos
      deleted: '1 year' // Datos anonimizados
    },
    members: {
      active: '10 years', // Miembros activos
      inactive: '5 years', // Ex-miembros
      minors: 'until_majority + 3 years' // Menores de edad
    },
    auditLogs: {
      security: '7 years', // Logs de seguridad
      access: '3 years', // Logs de acceso
      system: '1 year' // Logs del sistema
    },
    backups: {
      daily: '30 days',
      weekly: '12 weeks',
      monthly: '12 months',
      yearly: '7 years'
    }
  };
  
  static async enforceRetentionPolicies() {
    await this.cleanupExpiredUsers();
    await this.cleanupExpiredLogs();
    await this.cleanupExpiredBackups();
  }
  
  static async cleanupExpiredUsers() {
    const threeYearsAgo = new Date();
    threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
    
    // Encontrar usuarios inactivos por más de 3 años
    const expiredUsers = await strapi.entityService.findMany(
      'plugin::users-permissions.user',
      {
        filters: {
          lastLoginAt: { $lt: threeYearsAgo },
          isActive: false
        }
      }
    );
    
    for (const user of expiredUsers) {
      await this.anonymizeUser(user.id);
    }
  }
  
  static async anonymizeUser(userId) {
    const anonymizedData = {
      firstName: 'Usuario',
      lastName: 'Anonimizado',
      email: `anonimo-${userId}@example.com`,
      phone: null,
      address: null,
      isAnonymized: true,
      anonymizedAt: new Date()
    };
    
    await strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      { data: anonymizedData }
    );
  }
}

module.exports = DataRetentionService;
```

## Capacitación y Concienciación

### 1. Programa de Capacitación

#### Módulos de Capacitación

1. **Fundamentos de Seguridad**
   - Principios básicos de seguridad
   - Amenazas comunes
   - Responsabilidades del usuario

2. **Protección de Datos**
   - Manejo de información sensible
   - Derechos de los titulares
   - Procedimientos de privacidad

3. **Seguridad en el Desarrollo**
   - Codificación segura
   - Revisión de código
   - Testing de seguridad

4. **Respuesta a Incidentes**
   - Identificación de incidentes
   - Procedimientos de reporte
   - Primeros auxilios digitales

#### Evaluación y Certificación

```javascript
// src/api/security-training/content-types/training-record/schema.json
{
  "kind": "collectionType",
  "collectionName": "training_records",
  "info": {
    "singularName": "training-record",
    "pluralName": "training-records",
    "displayName": "Training Record"
  },
  "attributes": {
    "user": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "plugin::users-permissions.user"
    },
    "module": {
      "type": "enumeration",
      "enum": ["security_fundamentals", "data_protection", "secure_development", "incident_response"]
    },
    "completedAt": {
      "type": "datetime"
    },
    "score": {
      "type": "integer",
      "min": 0,
      "max": 100
    },
    "passed": {
      "type": "boolean",
      "default": false
    },
    "certificateUrl": {
      "type": "string"
    },
    "expiresAt": {
      "type": "datetime"
    }
  }
}
```

### 2. Métricas de Seguridad

```javascript
// src/utils/security-metrics.js
class SecurityMetrics {
  static async generateSecurityReport() {
    const report = {
      period: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 días
        end: new Date()
      },
      authentication: await this.getAuthMetrics(),
      vulnerabilities: await this.getVulnerabilityMetrics(),
      incidents: await this.getIncidentMetrics(),
      compliance: await this.getComplianceMetrics(),
      training: await this.getTrainingMetrics()
    };
    
    return report;
  }
  
  static async getAuthMetrics() {
    // Métricas de autenticación
    return {
      totalLogins: await this.countLogins(),
      failedLogins: await this.countFailedLogins(),
      mfaAdoption: await this.getMFAAdoptionRate(),
      passwordStrength: await this.getPasswordStrengthMetrics()
    };
  }
  
  static async getVulnerabilityMetrics() {
    // Métricas de vulnerabilidades
    return {
      openVulnerabilities: await this.countOpenVulnerabilities(),
      criticalVulnerabilities: await this.countCriticalVulnerabilities(),
      averageResolutionTime: await this.getAverageResolutionTime(),
      patchingCompliance: await this.getPatchingCompliance()
    };
  }
  
  static async getIncidentMetrics() {
    // Métricas de incidentes
    return {
      totalIncidents: await this.countIncidents(),
      incidentsByType: await this.getIncidentsByType(),
      averageResponseTime: await this.getAverageResponseTime(),
      incidentTrends: await this.getIncidentTrends()
    };
  }
  
  static async getComplianceMetrics() {
    // Métricas de cumplimiento
    return {
      dataRetentionCompliance: await this.getDataRetentionCompliance(),
      consentCompliance: await this.getConsentCompliance(),
      auditCompliance: await this.getAuditCompliance()
    };
  }
  
  static async getTrainingMetrics() {
    // Métricas de capacitación
    return {
      completionRate: await this.getTrainingCompletionRate(),
      averageScore: await this.getAverageTrainingScore(),
      certificationStatus: await this.getCertificationStatus()
    };
  }
}

module.exports = SecurityMetrics;
```

## Checklist de Seguridad

### Implementación Inicial
- [ ] Configurar autenticación JWT
- [ ] Implementar roles y permisos
- [ ] Configurar HTTPS/SSL
- [ ] Establecer rate limiting
- [ ] Configurar logging de seguridad
- [ ] Implementar validación de entrada
- [ ] Configurar CORS apropiadamente
- [ ] Establecer políticas de contraseñas
- [ ] Configurar backup encriptado
- [ ] Implementar monitoreo básico

### Seguridad Avanzada
- [ ] Implementar MFA
- [ ] Configurar encriptación de datos
- [ ] Establecer políticas de retención
- [ ] Implementar anonimización
- [ ] Configurar detección de intrusiones
- [ ] Establecer respuesta a incidentes
- [ ] Implementar auditoría completa
- [ ] Configurar alertas de seguridad
- [ ] Establecer programa de capacitación
- [ ] Realizar pruebas de penetración

### Mantenimiento Continuo
- [ ] Revisar logs de seguridad semanalmente
- [ ] Actualizar dependencias mensualmente
- [ ] Realizar auditorías de acceso trimestralmente
- [ ] Revisar políticas de seguridad semestralmente
- [ ] Realizar pruebas de respuesta a incidentes anualmente
- [ ] Actualizar capacitación de seguridad anualmente
- [ ] Revisar y actualizar documentación continuamente

## Recursos y Referencias

### Estándares y Frameworks
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [ISO 27001](https://www.iso.org/isoiec-27001-information-security.html)
- [CIS Controls](https://www.cisecurity.org/controls/)

### Herramientas de Seguridad
- [Strapi Security Guide](https://docs.strapi.io/dev-docs/security)
- [Node.js Security Checklist](https://blog.risingstack.com/node-js-security-checklist/)
- [OWASP Node.js Security](https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html)

### Contactos de Emergencia
- **Equipo de Seguridad**: <EMAIL>
- **Administrador del Sistema**: <EMAIL>
- **Respuesta a Incidentes**: <EMAIL>
- **Teléfono de Emergencia**: +1-XXX-XXX-XXXX

---

**Última actualización**: 28 de enero de 2025  
**Versión**: 1.0.0  
**Próxima revisión**: 28 de julio de 2025