/**
 * Controlador para el modelo Event
 * Maneja las operaciones CRUD para los eventos
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::event.event', ({ strapi }) => ({
  
  /**
   * Buscar todos los eventos con paginación
   */
  async find(ctx) {
    const result = await super.find(ctx);
    if (!result) {
      return { data: [], meta: { total: 0 } };
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Buscar un evento por ID
   */
  async findOne(ctx) {
    const result = await super.findOne(ctx);
    if (!result) {
      return ctx.notFound('Evento no encontrado');
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Crear un nuevo evento
   */
  async create(ctx) {
    const result = await super.create(ctx);
    if (!result) {
      return ctx.badRequest('Error al crear el evento');
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Actualizar un evento
   */
  async update(ctx) {
    const result = await super.update(ctx);
    if (!result) {
      return ctx.notFound('Evento no encontrado para actualizar');
    }
    const { data, meta } = result;
    return { data, meta };
  },

  /**
   * Eliminar un evento
   */
  async delete(ctx) {
    const result = await super.delete(ctx);
    if (!result) {
      return ctx.notFound('Evento no encontrado para eliminar');
    }
    const { data, meta } = result;
    return { data, meta };
  },
  
  // GET /api/events/hierarchical-data
  async getHierarchicalData(ctx) {
    try {
      const [fields, zones, districts, churches, users] = await Promise.all([
        strapi.entityService.findMany('api::field.field'),
        strapi.entityService.findMany('api::zone.zone'),
        strapi.entityService.findMany('api::district.district'),
        strapi.entityService.findMany('api::church.church'),
        strapi.entityService.findMany('plugin::users-permissions.user', { fields: ['ecclesiasticalRole'] })
      ]);

      const ecclesiasticalRoles = [...new Set(users.map(u => u.ecclesiasticalRole).filter(Boolean))];

      return { fields, zones, districts, churches, ecclesiasticalRoles };
    } catch (err) {
      ctx.body = err;
    }
  },

  // POST /api/events/:id/sessions/:sessionId/attendance
  async recordAttendance(ctx) {
    const { id: eventId, sessionId } = ctx.params;
    const { participantId, attended, notes, partialAttendance, partialAttendanceComment, timeStayed } = ctx.request.body;

    return await strapi.db.transaction(async ({ trx }) => {
      const event = await strapi.entityService.findOne('api::event.event', eventId, { populate: ['sessions'] }) as any;
      
      if (!event) return ctx.notFound('Evento no encontrado.');
      
      const sessionIndex = event.sessions.findIndex(s => s.sessionId == sessionId);
      if (sessionIndex === -1) return ctx.notFound('Sesión no encontrada.');

      const session = event.sessions[sessionIndex];

      if (session.event === 'pendiente' && attended) {
        event.sessions[sessionIndex].event = 'en_progreso';
        await strapi.entityService.update('api::event.event', eventId, { data: { sessions: event.sessions } });
      }

      const existingRecord = await strapi.db.query('api::attendance-record.attendance-record').findOne({
        where: { event: eventId, sessionId, participant: participantId },
      });

      const data = {
        sessionId,
        attended,
        notes,
        partialAttendance,
        partialAttendanceComment,
        timeStayed,
        event: eventId,
        participant: participantId,
      };

      if (existingRecord) {
        const updatedRecord = await strapi.entityService.update('api::attendance-record.attendance-record', existingRecord.id, { data });
        return updatedRecord;
      } else {
        const newRecord = await strapi.entityService.create('api::attendance-record.attendance-record', { data });
        return newRecord;
      }
    });
  },

  // POST /api/events/:id/sessions/:sessionId/complete-kiosk
  async completeKioskSession(ctx) {
    const { id: eventId, sessionId } = ctx.params;

    return await strapi.db.transaction(async ({ trx }) => {
        const event = await strapi.entityService.findOne('api::event.event', eventId, { populate: ['sessions', 'participants_invited'] }) as any;
        if (!event) return ctx.notFound('Evento no encontrado.');

        const sessionIndex = event.sessions.findIndex(s => s.sessionId == sessionId);
        if (sessionIndex === -1) return ctx.notFound('Sesión no encontrada.');
        
        const session = event.sessions[sessionIndex];
        if(session.attendanceMode !== 'kiosk') return ctx.badRequest('Esta función solo es para sesiones en modo kiosco.');

        const recordedParticipants = await strapi.db.query('api::attendance-record.attendance-record').findMany({
            where: { event: eventId, sessionId },
            populate: ['participant']
        });
        const recordedParticipantIds = new Set(recordedParticipants.map(r => r.participant.id));

        const unrecordedParticipants = event.participants_invited.filter(p => !recordedParticipantIds.has(p.id));

        for (const participant of unrecordedParticipants) {
            await strapi.entityService.create('api::attendance-record.attendance-record', {
                data: {
                    sessionId,
                    attended: false,
                    notes: 'Marcado automáticamente como ausente al completar sesión de kiosco.',
                    event: eventId,
                    participant: participant.id,
                }
            });
        }
        
        event.sessions[sessionIndex].eventStatus = 'completada';
        const allSessionsCompleted = event.sessions.every(s => s.eventStatus === 'completada' || s.eventStatus === 'cancelada');
        
        const dataToUpdate = {
            sessions: event.sessions,
            status: allSessionsCompleted ? 'completada' : event.eventStatus,
        };

        await strapi.entityService.update('api::event.event', eventId, { data: dataToUpdate });

        return { message: 'Sesión de kiosco completada exitosamente.' };
    });
  },

  // PATCH /api/events/:id/status
  async updateEventStatus(ctx) {
    const { id } = ctx.params;
    const { eventStatus } = ctx.request.body;

    const allowed = ['programada', 'en-progreso', 'completada', 'cancelada'];
    if (!allowed.includes(eventStatus)) {
      return ctx.badRequest('Estado no permitido.');
    }

    if (eventStatus === 'completada') {
      const event = await strapi.entityService.findOne('api::event.event', id, { populate: ['sessions'] }) as any;
      const canBeCompleted = event.sessions.every(s => s.eventStatus === 'completada' || s.eventStatus === 'cancelada');
      if (!canBeCompleted) {
        return ctx.badRequest('No se puede completar el evento. Aún hay sesiones pendientes o en progreso.');
      }
    }

    const updated = await strapi.entityService.update('api::event.event', id, {
      data: { eventStatus },
    });
    return updated;
  },

  // POST /api/events/:id/self-register
  async selfRegister(ctx) {
    const { id: eventId } = ctx.params;
    const participantData = ctx.request.body;

    const event = await strapi.entityService.findOne('api::event.event', eventId, { populate: ['participants_invited', 'sessions'] }) as any;
    if (!event || !event.autoRegistration) {
      return ctx.badRequest('Este evento no permite el auto-registro.');
    }

    let user = await strapi.query('plugin::users-permissions.user').findOne({ where: { email: participantData.email } });
    let message = '';

    if (!user) {
      user = await strapi.plugin('users-permissions').service('user').add({
        ...participantData,
        username: participantData.email,
        provider: 'local',
        confirmed: false,
        blocked: false,
        role: 2,
      });
      message = 'Usuario registrado en el sistema y añadido al evento.';
    } else {
      message = 'Usuario ya existente ha sido añadido al evento.';
    }

    const isAlreadyInvited = event.participants_invited.some(p => p.id === user.id);
    if (!isAlreadyInvited) {
      await strapi.entityService.update('api::event.event', eventId, {
        data: { participants_invited: { connect: [user.id] } } as any,
      });
    } else {
      message = 'Ya estabas registrado en este evento.';
    }

    const defaultSession = event.sessions.find(s => s.isDefault) || event.sessions[0];
    if (defaultSession) {
      await strapi.entityService.create('api::attendance-record.attendance-record', {
        data: {
          event: eventId,
          sessionId: defaultSession.sessionId,
          participant: user.id,
          attended: true,
        },
      });
    }

    ctx.body = { message };
  },

  // GET /api/users/:userId/events
  async findForUser(ctx) {
    const { userId } = ctx.params;
    const events = await strapi.entityService.findMany('api::event.event', {
      filters: { participants_invited: userId },
      populate: ['sessions', 'eventType'],
    });
    return events;
  },

  // POST /api/events/:id/justify-absence
  async justifyAbsence(ctx) {
    const { id } = ctx.params;
    const { participantId, sessionId, note } = ctx.request.body;

    let record = await strapi.db
      .query('api::attendance-record.attendance-record')
      .findOne({ where: { event: id, sessionId, participant: participantId } });

    if (record) {
      const updated = await strapi.entityService.update(
        'api::attendance-record.attendance-record',
        record.id,
        { data: { notes: note, attended: false } }
      );
      return updated;
    } else {
      const newRecord = await strapi.entityService.create('api::attendance-record.attendance-record', {
        data: { event: id, sessionId, participant: participantId, attended: false, notes: note }
      });
      return newRecord;
    }
  },

  // POST /api/events/:id/import-participants
  async importParticipants(ctx) {
    const { id } = ctx.params;
    const { participantIds } = ctx.request.body;

    if (!Array.isArray(participantIds)) {
      return ctx.badRequest('participantIds debe ser un arreglo');
    }

    const event = await strapi.entityService.findOne('api::event.event', id, {
      populate: ['participants_invited'],
    }) as any;
    const ids = new Set(event.participants_invited.map(p => p.id));
    participantIds.forEach(pid => ids.add(pid));

    await strapi.entityService.update('api::event.event', id, {
      data: { participants_invited: Array.from(ids) } as any,
    });

    ctx.body = { added: participantIds.length };
  },

  // POST /api/events/:id/validate-participant
  async validateParticipant(ctx) {
    const { id: eventId } = ctx.params;
    const participantData = ctx.request.body;

    const event = await strapi.entityService.findOne('api::event.event', eventId, { populate: ['participants_invited'] }) as any;
    if (!event) return ctx.notFound('Evento no encontrado.');

    const existingUser = await strapi.query('plugin::users-permissions.user').findOne({ where: { email: participantData.email } });

    const result = {
      existsInSystem: !!existingUser,
      existsInEvent: !!existingUser && event.participants_invited.some(p => p.id === existingUser.id),
      hasDataDifferences: false,
      message: '',
      actionRequired: 'none'
    };

    if (existingUser) {
      const fieldsToCompare = ['firstName', 'lastName', 'phone'];
      result.hasDataDifferences = fieldsToCompare.some(field => existingUser[field] !== participantData[field]);
    }

    // Determine message and required action
    if (!existingUser) {
      result.message = 'Usuario no encontrado en el sistema.';
      result.actionRequired = 'createUser';
    } else if (!result.existsInEvent) {
      result.message = 'Usuario registrado en el sistema pero no inscrito en el evento.';
      result.actionRequired = result.hasDataDifferences ? 'updateUserData' : 'addToEvent';
    } else if (result.hasDataDifferences) {
      result.message = 'Usuario inscrito pero con información diferente.';
      result.actionRequired = 'updateUserData';
    } else {
      result.message = 'Usuario ya inscrito en el evento.';
      result.actionRequired = 'none';
    }

    ctx.body = result;
  },

  // POST /api/events/:eventId/register-participant
  async registerParticipantAndMarkAttendance(ctx) {
    const { eventId } = ctx.params;
    const participantData = ctx.request.body;

    const event = await strapi.entityService.findOne('api::event.event', eventId, { populate: ['participants_invited', 'sessions'] }) as any;
    if (!event) return ctx.notFound('Evento no encontrado.');

    let user = await strapi.query('plugin::users-permissions.user').findOne({ where: { email: participantData.email } });
    if (!user) {
      user = await strapi.plugin('users-permissions').service('user').add({
        ...participantData,
        username: participantData.email,
        provider: 'local',
        confirmed: false,
        blocked: false,
        role: 2,
      });
    }

    const isAlreadyInvited = event.participants_invited.some(p => p.id === user.id);
    if (!isAlreadyInvited) {
      await strapi.entityService.update('api::event.event', eventId, {
        data: { participants_invited: { connect: [user.id] } } as any,
      });
    }

    const defaultSession = event.sessions.find(s => s.isDefault) || event.sessions[0];
    if (defaultSession) {
      await strapi.entityService.create('api::attendance-record.attendance-record', {
        data: {
          event: eventId,
          sessionId: defaultSession.sessionId,
          participant: user.id,
          attended: true,
        },
      });
    }

    ctx.body = { message: 'Participante registrado y asistencia marcada.' };
  },

  // POST /api/events/:eventId/sessions
  async addSession(ctx) {
    const { eventId } = ctx.params;
    const session = ctx.request.body;

    const event = await strapi.entityService.findOne('api::event.event', eventId, {
      populate: ['sessions'],
    }) as any;

    await strapi.entityService.update('api::event.event', eventId, {
      data: { sessions: [...event.sessions, session] },
    });

    ctx.body = { message: 'Sesión agregada' };
  },

  // PUT /api/events/:eventId/sessions/:sessionId
  async updateSession(ctx) {
    const { eventId, sessionId } = ctx.params;
    const changes = ctx.request.body;

    const event = await strapi.entityService.findOne('api::event.event', eventId, {
      populate: ['sessions'],
    }) as any;

    const index = event.sessions.findIndex(s => s.sessionId == sessionId);
    if (index === -1) return ctx.notFound('Sesión no encontrada.');
    event.sessions[index] = { ...event.sessions[index], ...changes };

    await strapi.entityService.update('api::event.event', eventId, {
      data: { sessions: event.sessions },
    });

    ctx.body = { message: 'Sesión actualizada' };
  },

  // DELETE /api/events/:eventId/sessions/:sessionId
  async deleteSession(ctx) {
    const { eventId, sessionId } = ctx.params;

    const event = await strapi.entityService.findOne('api::event.event', eventId, {
      populate: ['sessions'],
    }) as any;

    const filtered = event.sessions.filter(s => s.sessionId != sessionId);
    await strapi.entityService.update('api::event.event', eventId, {
      data: { sessions: filtered },
    });

    ctx.body = { message: 'Sesión eliminada' };
  },

}));
