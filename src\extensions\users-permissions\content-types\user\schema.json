{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false, "required": true, "default": "local"}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "phone": {"type": "string"}, "ecclesiasticalRole": {"type": "string"}, "avatar": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "fieldAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::field.field"}, "zoneAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::zone.zone"}, "districtAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::district.district"}, "churchAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::church.church"}, "participantProfile": {"type": "relation", "relation": "oneToOne", "target": "api::participant.participant", "mappedBy": "userAccount"}}}