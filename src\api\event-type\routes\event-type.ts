/**
 * Rutas para el modelo EventType
 * Define los endpoints disponibles para los tipos de eventos
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/event-types',
      handler: 'event-type.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/event-types/:id',
      handler: 'event-type.findOne',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/event-types',
      handler: 'event-type.create',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/event-types/:id',
      handler: 'event-type.update',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'DELETE',
      path: '/event-types/:id',
      handler: 'event-type.delete',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/event-types/:id/stats',
      handler: 'event-type.getStats',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};