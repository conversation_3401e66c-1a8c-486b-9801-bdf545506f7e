/**
 * Servicio para el modelo Department
 * Contiene la lógica de negocio para los departamentos
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::department.department', ({ strapi }) => ({
  /**
   * Buscar departamentos con filtros personalizados
   */
  async findWithFilters(params = {}) {
    return await strapi.entityService.findMany('api::department.department', {
      ...params,
      populate: {
        events: {
          fields: ['id', 'title', 'date', 'eventStatus']
        }
      }
    });
  },

  /**
   * Obtener un departamento por nombre
   */
  async findByName(name: string) {
    return await strapi.entityService.findMany('api::department.department', {
      filters: {
        name: {
          $eq: name
        }
      },
      limit: 1
    });
  },

  /**
   * Obtener departamentos activos
   */
  async findActive() {
    return await strapi.entityService.findMany('api::department.department', {
      filters: {
        isActive: true
      },
      sort: { name: 'asc' }
    });
  },

  /**
   * Crear un departamento si no existe
   */
  async createIfNotExists(departmentData: any) {
    const existing = await this.findByName(departmentData.name);
    
    if (existing && existing.length > 0) {
      return existing[0];
    }

    return await strapi.entityService.create('api::department.department', {
      data: departmentData
    });
  },

  /**
   * Obtener estadísticas de eventos por departamento
   */
  async getEventStats(departmentId: number) {
    const department = await strapi.entityService.findOne('api::department.department', departmentId);

    if (!department) {
      throw new Error('Departamento no encontrado');
    }

    // Obtener eventos relacionados con este departamento
    const events = await strapi.entityService.findMany('api::event.event', {
      filters: {
        invitingDepartment: {
          id: departmentId
        }
      },
      fields: ['id', 'eventStatus', 'date']
    }) as any[];
    const stats = {
      total: events.length,
      programadas: events.filter(e => e.eventStatus === 'programada').length,
      enProgreso: events.filter(e => e.eventStatus === 'en-progreso').length,
      completadas: events.filter(e => e.eventStatus === 'completada').length,
      canceladas: events.filter(e => e.eventStatus === 'cancelada').length
    };

    return {
      department: {
        id: department.id,
        name: department.name,
        description: department.description
      },
      eventStats: stats
    };
  }
}));