# 🏛️ SCMP - Sistema de Competencia y Ministerio Pastoral

[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![Strapi Version](https://img.shields.io/badge/strapi-v5.0.0-blue.svg)](https://strapi.io/)
[![PostgreSQL](https://img.shields.io/badge/postgresql-%3E%3D13-blue.svg)](https://www.postgresql.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## 📋 Descripción

**SCMP Backend & Back Office** - Sistema de Competencia y Ministerio Pastoral desarrollado con **Strapi v5**.

### ⚠️ Importante: Arquitectura del Sistema

Esta aplicación constituye **únicamente el backend y back office** de un sistema más amplio:

- **🔧 Backend API**: Proporciona todos los endpoints REST para la gestión de datos
- **🖥️ Back Office**: Panel administrativo de Strapi para mantenimiento del sistema
- **📊 Gestión de Datos Maestros**: Mantenimiento de iglesias, campos, zonas, distritos y configuraciones

### 🏗️ Arquitectura Completa del Sistema

```
┌─────────────────────┐    ┌─────────────────────┐
│   Frontend React    │    │  Backend & Back     │
│   (Aplicación       │◄──►│  Office (Esta App)  │
│   Separada)         │    │                     │
└─────────────────────┘    └─────────────────────┘
│                      │    │                     │
│ • Creación reuniones │    │ • API REST          │
│ • Gestión agenda     │    │ • Panel Admin       │
│ • Marcar asistencia  │    │ • Datos maestros    │
│ • Procesos operativos│    │ • Configuraciones   │
└─────────────────────┘    └─────────────────────┘
```

### 🎯 Responsabilidades de Esta Aplicación

**✅ Lo que SÍ maneja:**
- Gestión de la estructura organizacional (Fields → Zones → Districts → Churches → Members)
- Mantenimiento de datos maestros
- Configuraciones del sistema
- API REST para consumo del frontend
- Panel administrativo para configuración

**❌ Lo que NO maneja (Frontend React):**
- Creación y gestión de reuniones
- Gestión de agenda
- Marcado de asistencia
- Procesos operativos diarios
- Interfaz de usuario final

Este sistema maneja una jerarquía completa de **Fields → Zones → Districts → Churches → Members** con funcionalidades avanzadas de autenticación, autorización y gestión de datos, proporcionando la base para que el frontend React implemente los procesos operativos.

## ✨ Características Principales

- 🏗️ **Gestión jerárquica** de la estructura organizacional
- 🔐 **Sistema de autenticación y autorización** con roles granulares
- 🚀 **API REST completa** con documentación detallada
- 🗄️ **Base de datos PostgreSQL** con migraciones automáticas
- 📊 **Sistema de seeds** con datos iniciales (355 registros)
- 🛡️ **Seguridad avanzada** con rate limiting y validación
- 📚 **Documentación completa** y guías detalladas
- 🔧 **Configuración de desarrollo** optimizada

## 🏗️ Estructura de Datos

### Jerarquía Organizacional

```
📍 Field (Campo) - 4 registros
├── 🌍 Zone (Zona) - 27 registros
│   ├── 🏘️ District (Distrito) - 81 registros
│   │   ├── ⛪ Church (Iglesia) - 243 registros
│   │   │   └── 👤 Member (Miembro) - Ilimitados
```

### Entidades Principales

| Entidad | Descripción | Cantidad Inicial |
|---------|-------------|------------------|
| **Field** | Nivel más alto de la organización | 4 |
| **Zone** | Subdivisión geográfica de un campo | 27 |
| **District** | Subdivisión administrativa de una zona | 81 |
| **Church** | Iglesia local dentro de un distrito | 243 |
| **Member** | Miembro individual de una iglesia | 0 (por configurar) |

## 🚀 Inicio Rápido

### Prerrequisitos

- **Node.js** 18.0.0 o superior
- **PostgreSQL** 13 o superior
- **npm** o **yarn**

### Instalación

1. **Clonar el repositorio**:
```bash
git clone <repository-url>
cd scmp-cm
```

2. **Instalar dependencias**:
```bash
npm install
```

3. **Configurar variables de entorno**:
```bash
cp .env.example .env
```

4. **Configurar base de datos**:
```sql
-- En PostgreSQL
CREATE DATABASE scmp_development;
CREATE USER scmp_user WITH ENCRYPTED PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE scmp_development TO scmp_user;
```

5. **Ejecutar la aplicación**:
```bash
npm run develop
```

🎉 **¡Listo!** La aplicación estará disponible en `http://localhost:1337`

## 📖 Documentación

### 📚 Guías Principales

| Documento | Descripción | Enlace |
|-----------|-------------|--------|
| 🏗️ **Arquitectura de BD** | Esquemas, relaciones y diagramas | [`docs/database/architecture.md`](docs/database/architecture.md) |
| 🌱 **Seeds y Migraciones** | Sistema de datos iniciales | [`docs/database/seeds-migrations.md`](docs/database/seeds-migrations.md) |
| 💻 **Configuración de Desarrollo** | Setup y herramientas | [`docs/development/setup.md`](docs/development/setup.md) |
| 🤝 **Guía de Contribución** | Estándares y procesos | [`docs/development/contributing.md`](docs/development/contributing.md) |
| 🚀 **Guía de Despliegue** | Producción y staging | [`docs/deployment/guide.md`](docs/deployment/guide.md) |
| 📡 **Referencia de API** | Endpoints y ejemplos | [`docs/api/reference.md`](docs/api/reference.md) |
| 🔒 **Guía de Seguridad** | Políticas y procedimientos | [`docs/security/security.md`](docs/security/security.md) |
| 📋 **Changelog** | Historial de cambios | [`docs/CHANGELOG.md`](docs/CHANGELOG.md) |

### 🔗 Enlaces Rápidos

- 📊 **[Documentación Principal](docs/README.md)** - Índice completo de documentación
- 🏗️ **[Arquitectura del Sistema](docs/database/architecture.md)** - Diseño técnico detallado
- 🚀 **[Guía de Desarrollo](docs/development/setup.md)** - Configuración paso a paso
- 📡 **[API Reference](docs/api/reference.md)** - Documentación completa de endpoints

## 🛠️ Uso

### Panel de Administración

1. Accede a `http://localhost:1337/admin`
2. Crea tu primer usuario administrador
3. Explora las entidades precargadas

### API REST

Base URL: `http://localhost:1337/api/`

#### Endpoints Principales

```bash
# Fields (Campos)
GET    /api/fields              # Listar todos los campos
GET    /api/fields/:id          # Obtener campo específico
POST   /api/fields              # Crear nuevo campo
PUT    /api/fields/:id          # Actualizar campo
DELETE /api/fields/:id          # Eliminar campo

# Zones (Zonas)
GET    /api/zones               # Listar todas las zonas
GET    /api/zones/:id           # Obtener zona específica

# Districts (Distritos)
GET    /api/districts           # Listar todos los distritos
GET    /api/districts/:id       # Obtener distrito específico

# Churches (Iglesias)
GET    /api/churches            # Listar todas las iglesias
GET    /api/churches/:id        # Obtener iglesia específica

# Members (Miembros)
GET    /api/members             # Listar todos los miembros
POST   /api/members             # Crear nuevo miembro
```

#### Ejemplos de Uso

```bash
# Obtener todas las iglesias con sus distritos
curl "http://localhost:1337/api/churches?populate=district"

# Buscar iglesias por nombre
curl "http://localhost:1337/api/churches?filters[name][$contains]=Central"

# Paginación
curl "http://localhost:1337/api/churches?pagination[page]=1&pagination[pageSize]=10"
```

## 📊 Datos Iniciales

El sistema incluye un **sistema de migraciones automáticas** que carga datos iniciales estructurados:

### 📈 Distribución de Datos

| Entidad | Cantidad | Distribución |
|---------|----------|-------------|
| **Fields** | 4 | Campo Central, Norte, Sur, Oeste |
| **Zones** | 27 | 6-8 zonas por campo |
| **Districts** | 81 | 3 distritos por zona |
| **Churches** | 243 | 3 iglesias por distrito |

### 🔄 Carga Automática

Los datos se cargan automáticamente:
- ✅ Al iniciar la aplicación por primera vez
- ✅ Usando tablas de enlace de Strapi v5
- ✅ Con relaciones jerárquicas completas
- ✅ Respetando la integridad referencial

## 💻 Desarrollo

### Scripts Disponibles

```bash
# Desarrollo
npm run develop          # Modo desarrollo con hot reload
npm run start           # Modo producción
npm run build           # Construir para producción

# Herramientas
npm run strapi          # CLI de Strapi
npm run lint            # Verificar código con ESLint
npm run lint:fix        # Corregir problemas automáticamente
npm run format          # Formatear código con Prettier

# Testing
npm run test            # Ejecutar pruebas
npm run test:watch      # Ejecutar pruebas en modo watch
```

### Estructura del Proyecto

```
scmp-cm/
├── 📁 src/
│   ├── 📁 api/              # Definiciones de API
│   │   ├── 📁 field/        # API de campos
│   │   ├── 📁 zone/         # API de zonas
│   │   ├── 📁 district/     # API de distritos
│   │   ├── 📁 church/       # API de iglesias
│   │   └── 📁 member/       # API de miembros
│   ├── 📁 components/       # Componentes reutilizables
│   └── 📁 extensions/       # Extensiones personalizadas
├── 📁 config/              # Configuración de Strapi
├── 📁 database/            # Migraciones y seeds
│   └── 📁 migrations/      # Archivos de migración
├── 📁 docs/                # Documentación completa
├── 📁 public/              # Archivos estáticos
└── 📄 package.json         # Dependencias y scripts
```

## 🔒 Seguridad

### Características de Seguridad

- 🔐 **Autenticación JWT** con tokens seguros
- 👥 **Sistema de roles** granular (Super Admin, Admin, Pastor, Leader, Member, Visitor)
- 🛡️ **Rate limiting** para prevenir ataques
- ✅ **Validación de entrada** estricta
- 🧹 **Sanitización de datos** automática
- 📝 **Logging de seguridad** detallado
- 🔒 **Encriptación** de campos sensibles

### Roles del Sistema

| Rol | Permisos | Descripción |
|-----|----------|-------------|
| **Super Admin** | Todos | Control total del sistema |
| **Admin** | Gestión completa | Campos, zonas, distritos, iglesias |
| **Pastor** | Su iglesia | Gestión de su iglesia y miembros |
| **Leader** | Asistencia | Ayuda en gestión de miembros |
| **Member** | Solo lectura | Información básica de su iglesia |
| **Visitor** | Público | Solo información pública |

## 🚀 Despliegue

### Opciones de Despliegue

- 🖥️ **VPS/Servidor Dedicado** - Control completo
- 🐳 **Docker** - Containerización
- ☁️ **Heroku** - Despliegue rápido
- 🌊 **DigitalOcean App Platform** - Escalabilidad
- 🚀 **AWS Elastic Beanstalk** - Infraestructura robusta

### Configuración de Producción

```env
NODE_ENV=production
HOST=0.0.0.0
PORT=1337

# Generar claves seguras para producción
APP_KEYS=secure_key_1,secure_key_2,secure_key_3,secure_key_4
API_TOKEN_SALT=secure_api_salt
ADMIN_JWT_SECRET=secure_admin_secret
JWT_SECRET=secure_jwt_secret

# Base de datos de producción
DATABASE_CLIENT=postgres
DATABASE_HOST=your_db_host
DATABASE_PORT=5432
DATABASE_NAME=scmp_production
DATABASE_USERNAME=scmp_user
DATABASE_PASSWORD=secure_password
DATABASE_SSL=true
```

## 🤝 Contribución

¡Las contribuciones son bienvenidas! Por favor lee nuestra **[Guía de Contribución](docs/development/contributing.md)** para conocer nuestros estándares y procesos.

### Proceso de Contribución

1. 🍴 **Fork** el proyecto
2. 🌿 **Crea una rama** para tu feature (`git checkout -b feature/AmazingFeature`)
3. ✅ **Commit** tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. 📤 **Push** a la rama (`git push origin feature/AmazingFeature`)
5. 🔄 **Abre un Pull Request**

### Estándares de Código

- 📏 **ESLint** para calidad de código
- 🎨 **Prettier** para formateo consistente
- 📝 **Conventional Commits** para mensajes claros
- 🧪 **Testing** obligatorio para nuevas funcionalidades

## 📊 Roadmap

### Versión 1.1.0 (Q2 2025)
- [ ] 📊 Dashboard administrativo
- [ ] 📈 Reportes y estadísticas
- [ ] 📤 Exportación de datos
- [ ] 📧 Notificaciones por email

### Versión 1.2.0 (Q3 2025)
- [ ] 📱 Aplicación móvil
- [ ] 🔄 Sincronización offline
- [ ] 📍 Geolocalización de iglesias
- [ ] 📅 Sistema de eventos

### Versión 2.0.0 (Q4 2025)
- [ ] 🏗️ Arquitectura de microservicios
- [ ] 🤖 Machine Learning para insights
- [ ] 🔗 Integración con sistemas externos
- [ ] 🏢 Multi-tenancy

## 📄 Licencia

Este proyecto está bajo la **Licencia MIT**. Ver el archivo [`LICENSE`](LICENSE) para más detalles.

## 🆘 Soporte

### Canales de Soporte

- 📚 **Documentación**: Consulta la [documentación completa](docs/README.md)
- 🐛 **Issues**: Reporta problemas en [GitHub Issues](https://github.com/your-org/scmp-cm/issues)
- 📧 **Email**: Contacta a `<EMAIL>`
- 💬 **Chat**: Únete a nuestro Discord/Slack (interno)

### Información del Proyecto

- **Versión Actual**: 1.0.0
- **Fecha de Lanzamiento**: 28 de enero de 2025
- **Mantenido por**: Equipo de Desarrollo SCMP
- **Compatibilidad**: Node.js 18+, PostgreSQL 13+

---

<div align="center">

**Desarrollado con ❤️ para la gestión eficiente del ministerio pastoral**

[Documentación](docs/README.md) • [API Reference](docs/api/reference.md) • [Contribuir](docs/development/contributing.md) • [Seguridad](docs/security/security.md)

</div>
