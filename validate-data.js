const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// Configuración de la base de datos
const dbConfig = {
  host: 'aws-0-us-east-2.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.ktykxlboodjvnlabbwoy',
  password: 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};

async function validateData() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');
    
    // Leer datos esperados del archivo JSON
    const seedDataPath = path.join(__dirname, 'database', 'seeds', 'initial-data.json');
    const seedData = JSON.parse(fs.readFileSync(seedDataPath, 'utf8'));
    
    // Número esperado de tipos de eventos (según migración)
    const expectedEventTypes = 22;
    
    console.log('\n📊 TOTALES ESPERADOS:');
    console.log(`   - Fields: ${seedData.fields.length}`);
    console.log(`   - Zones: ${seedData.zones.length}`);
    console.log(`   - Districts: ${seedData.districts.length}`);
    console.log(`   - Churches: ${seedData.churches.length}`);
    console.log(`   - Event Types: ${expectedEventTypes}`);
    
    // Consultar totales reales en la base de datos
    console.log('\n📊 TOTALES REALES (en base de datos):');
    
    const fieldsResult = await client.query('SELECT COUNT(*) as count FROM fields');
    const fieldsCount = parseInt(fieldsResult.rows[0].count);
    console.log(`   - Fields: ${fieldsCount}`);
    
    const zonesResult = await client.query('SELECT COUNT(*) as count FROM zones');
    const zonesCount = parseInt(zonesResult.rows[0].count);
    console.log(`   - Zones: ${zonesCount}`);
    
    const districtsResult = await client.query('SELECT COUNT(*) as count FROM districts');
    const districtsCount = parseInt(districtsResult.rows[0].count);
    console.log(`   - Districts: ${districtsCount}`);
    
    const churchesResult = await client.query('SELECT COUNT(*) as count FROM churches');
    const churchesCount = parseInt(churchesResult.rows[0].count);
    console.log(`   - Churches: ${churchesCount}`);
    
    const eventTypesResult = await client.query('SELECT COUNT(*) as count FROM event_types');
    const eventTypesCount = parseInt(eventTypesResult.rows[0].count);
    console.log(`   - Event Types: ${eventTypesCount}`);
    
    // Validación y comparación
    console.log('\n✅ VALIDACIÓN:');
    
    const fieldsValid = fieldsCount === seedData.fields.length;
    const zonesValid = zonesCount === seedData.zones.length;
    const districtsValid = districtsCount === seedData.districts.length;
    const churchesValid = churchesCount === seedData.churches.length;
    const eventTypesValid = eventTypesCount === expectedEventTypes;
    
    console.log(`   - Fields: ${fieldsValid ? '✅' : '❌'} (${fieldsCount}/${seedData.fields.length})`);
    console.log(`   - Zones: ${zonesValid ? '✅' : '❌'} (${zonesCount}/${seedData.zones.length})`);
    console.log(`   - Districts: ${districtsValid ? '✅' : '❌'} (${districtsCount}/${seedData.districts.length})`);
    console.log(`   - Churches: ${churchesValid ? '✅' : '❌'} (${churchesCount}/${seedData.churches.length})`);
    console.log(`   - Event Types: ${eventTypesValid ? '✅' : '❌'} (${eventTypesCount}/${expectedEventTypes})`);
    
    const allValid = fieldsValid && zonesValid && districtsValid && churchesValid && eventTypesValid;
    
    console.log('\n📈 RESULTADO FINAL:');
    if (allValid) {
      console.log('✅ ¡TODOS LOS DATOS ESTÁN COMPLETOS!');
    } else {
      console.log('❌ FALTAN DATOS POR INSERTAR');
      
      if (!fieldsValid) console.log(`   - Faltan ${seedData.fields.length - fieldsCount} Fields`);
      if (!zonesValid) console.log(`   - Faltan ${seedData.zones.length - zonesCount} Zones`);
      if (!districtsValid) console.log(`   - Faltan ${seedData.districts.length - districtsCount} Districts`);
      if (!churchesValid) console.log(`   - Faltan ${seedData.churches.length - churchesCount} Churches`);
      if (!eventTypesValid) console.log(`   - Faltan ${expectedEventTypes - eventTypesCount} Event Types`);
    }
    
  } catch (error) {
    console.error('❌ Error durante la validación:', error.message);
  } finally {
    await client.end();
    console.log('\n🔌 Conexión cerrada');
  }
}

validateData();