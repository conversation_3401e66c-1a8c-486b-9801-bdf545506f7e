/**
 * Migración de Strapi v5 para corregir documentId faltantes en tipos de eventos
 * Fecha: 2025-01-28
 * Descripción: Genera documentId únicos para todos los tipos de eventos existentes que no lo tienen
 */

const { v4: uuidv4 } = require('uuid');

/**
 * Función para generar un documentId único en formato Strapi v5
 * Strapi v5 usa un formato específico para documentId
 */
function generateDocumentId() {
  // Generar UUID v4 y remover guiones para formato Strapi
  return uuidv4().replace(/-/g, '');
}

/**
 * Función para verificar si un documentId ya existe
 */
async function documentIdExists(knex, documentId, tableName = 'event_types') {
  try {
    const existing = await knex(tableName)
      .where({ document_id: documentId })
      .first();
    return !!existing;
  } catch (error) {
    console.error(`Error al verificar documentId ${documentId}:`, error.message);
    return false;
  }
}

/**
 * Función para generar un documentId único que no exista en la tabla
 */
async function generateUniqueDocumentId(knex, tableName = 'event_types') {
  let documentId;
  let attempts = 0;
  const maxAttempts = 10;
  
  do {
    documentId = generateDocumentId();
    attempts++;
    
    if (attempts > maxAttempts) {
      throw new Error(`No se pudo generar un documentId único después de ${maxAttempts} intentos`);
    }
  } while (await documentIdExists(knex, documentId, tableName));
  
  return documentId;
}

module.exports = {
  /**
   * Migración hacia adelante (up)
   */
  async up(knex) {
    console.log('🚀 Iniciando corrección de documentId para tipos de eventos...');
    
    try {
      // Verificar si la tabla existe
      const tableExists = await knex.schema.hasTable('event_types');
      
      if (!tableExists) {
        console.log('⚠️  La tabla event_types no existe. Saltando migración.');
        return;
      }
      
      // Verificar si la columna document_id existe
      const hasDocumentIdColumn = await knex.schema.hasColumn('event_types', 'document_id');
      
      if (!hasDocumentIdColumn) {
        console.log('⚠️  La columna document_id no existe. Saltando migración.');
        console.log('💡 Ejecute primero las migraciones de Strapi para crear la columna.');
        return;
      }
      
      // Obtener todos los tipos de eventos sin documentId
      const eventTypesWithoutDocumentId = await knex('event_types')
        .whereNull('document_id')
        .orWhere('document_id', '')
        .select(['id', 'name']);
      
      console.log(`📋 Tipos de eventos sin documentId encontrados: ${eventTypesWithoutDocumentId.length}`);
      
      if (eventTypesWithoutDocumentId.length === 0) {
        console.log('✅ Todos los tipos de eventos ya tienen documentId. No se requiere acción.');
        return;
      }
      
      let updatedCount = 0;
      let errorCount = 0;
      
      // Procesar cada tipo de evento sin documentId
      for (const eventType of eventTypesWithoutDocumentId) {
        try {
          console.log(`\n🔧 Procesando: ${eventType.name} (ID: ${eventType.id})`);
          
          // Generar documentId único
          const documentId = await generateUniqueDocumentId(knex, 'event_types');
          
          // Actualizar el tipo de evento con el nuevo documentId
          const updateResult = await knex('event_types')
            .where({ id: eventType.id })
            .update({
              document_id: documentId,
              updated_at: new Date()
            });
          
          if (updateResult > 0) {
            console.log(`✅ DocumentId asignado: ${documentId}`);
            updatedCount++;
          } else {
            console.log(`⚠️  No se pudo actualizar el tipo de evento ${eventType.name}`);
            errorCount++;
          }
          
        } catch (error) {
          console.error(`❌ Error procesando ${eventType.name}:`, error.message);
          errorCount++;
        }
      }
      
      // Resumen
      console.log('\n📊 RESUMEN DE CORRECCIÓN:');
      console.log(`✅ Tipos de eventos actualizados: ${updatedCount}`);
      console.log(`❌ Errores encontrados: ${errorCount}`);
      console.log(`📋 Total procesados: ${eventTypesWithoutDocumentId.length}`);
      
      if (errorCount === 0) {
        console.log('🎉 Corrección de documentId completada exitosamente!');
      } else {
        console.log('⚠️  Corrección completada con algunos errores.');
      }
      
      // Verificación final
      const remainingWithoutDocumentId = await knex('event_types')
        .whereNull('document_id')
        .orWhere('document_id', '')
        .count('id as count');
      
      const remaining = parseInt(remainingWithoutDocumentId[0].count);
      console.log(`\n🔍 Verificación final: ${remaining} tipos de eventos sin documentId`);
      
    } catch (error) {
      console.error('❌ Error en migración de corrección de documentId:', error.message);
      throw error;
    }
  },

  /**
   * Migración hacia atrás (down)
   * ADVERTENCIA: Esta operación eliminará todos los documentId generados
   */
  async down(knex) {
    console.log('🔄 Revirtiendo corrección de documentId...');
    console.log('⚠️  ADVERTENCIA: Esta operación eliminará todos los documentId generados por esta migración.');
    
    try {
      // Verificar si la tabla existe
      const tableExists = await knex.schema.hasTable('event_types');
      
      if (!tableExists) {
        console.log('⚠️  La tabla event_types no existe. No se requiere acción.');
        return;
      }
      
      // Verificar si la columna document_id existe
      const hasDocumentIdColumn = await knex.schema.hasColumn('event_types', 'document_id');
      
      if (!hasDocumentIdColumn) {
        console.log('⚠️  La columna document_id no existe. No se requiere acción.');
        return;
      }
      
      // Contar tipos de eventos con documentId
      const eventTypesWithDocumentId = await knex('event_types')
        .whereNotNull('document_id')
        .andWhere('document_id', '!=', '')
        .count('id as count');
      
      const count = parseInt(eventTypesWithDocumentId[0].count);
      console.log(`📋 Tipos de eventos con documentId encontrados: ${count}`);
      
      if (count === 0) {
        console.log('✅ No hay documentId para eliminar.');
        return;
      }
      
      // Eliminar todos los documentId (establecer como NULL)
      const updateResult = await knex('event_types')
        .whereNotNull('document_id')
        .andWhere('document_id', '!=', '')
        .update({
          document_id: null,
          updated_at: new Date()
        });
      
      console.log(`🗑️  DocumentId eliminados de ${updateResult} tipos de eventos`);
      console.log('✅ Reversión completada');
      
    } catch (error) {
      console.error('❌ Error en reversión de migración:', error.message);
      throw error;
    }
  }
};
