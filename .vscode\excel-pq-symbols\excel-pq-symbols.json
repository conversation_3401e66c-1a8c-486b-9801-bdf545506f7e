[{"name": "Excel.CurrentWorkbook", "documentation": {"description": "Returns the contents of the current Excel workbook.", "longDescription": "Returns tables, named ranges, and dynamic arrays. Unlike Excel.Workbook, it does not return sheets.", "category": "Accessing data"}, "functionParameters": [], "completionItemKind": 3, "isDataSource": true, "type": "table"}, {"name": "Documentation", "documentation": {"description": "Contains properties for function documentation metadata", "category": "Documentation"}, "functionParameters": [], "completionItemKind": 9, "isDataSource": false, "type": "record", "fields": {"Name": {"type": "text"}, "Description": {"type": "text"}, "Parameters": {"type": "record"}}}]