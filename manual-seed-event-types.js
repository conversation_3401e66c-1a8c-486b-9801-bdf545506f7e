const { Client } = require('pg');

// Configuración de la base de datos con credenciales de Supabase
const client = new Client({
  host: 'aws-0-us-east-2.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.ktykxlboodjvnlabbwoy',
  password: 'Gran<PERSON>@1844',
  ssl: {
    rejectUnauthorized: false // Deshabilitar verificación SSL estricta
  }
});

// Datos de tipos de eventos extraídos de la migración
const eventTypesData = [
  { id: 1,  name: 'Simposio',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 2,  name: 'Congreso',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 3,  name: 'Encuentro',              autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 4,  name: 'Instrucción',            autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 5,  name: 'Entrenamiento',          autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 6,  name: 'Curso',                  autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 7,  name: 'Taller',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 8,  name: 'Jornada',                autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 9,  name: 'Capacitación',           autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 10, name: 'Certificación',          autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 11, name: 'Escuela',                autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 12, name: 'INCALA',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 13, name: 'Campamento',             autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 14, name: 'Retiro',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 15, name: 'Cumbre',                 autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 16, name: 'Camporee',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 17, name: 'Seminario',              autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 18, name: 'Seminario Taller',       autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 19, name: 'Convención',             autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' },
  { id: 20, name: 'Reunión',                autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 21, name: 'Reunión de Instrucción', autoRegistrationDefault: false, attendanceMethodDefault: 'manual' },
  { id: 22, name: 'Festival',               autoRegistrationDefault: true,  attendanceMethodDefault: 'kiosco_rapido' }
];

async function seedEventTypes() {
  try {
    console.log('🔗 Conectando a la base de datos...');
    await client.connect();
    console.log('✅ Conectado exitosamente');

    // Verificar si la tabla existe
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'event_types'
      );
    `;
    
    const tableExistsResult = await client.query(tableExistsQuery);
    const tableExists = tableExistsResult.rows[0].exists;
    
    if (!tableExists) {
      console.log('⚠️  La tabla event_types no existe. Creando tabla...');
      
      // Crear tabla si no existe
      await client.query(`
        CREATE TABLE IF NOT EXISTS event_types (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL UNIQUE,
          auto_registration_default BOOLEAN DEFAULT true,
          attendance_method_default VARCHAR(50) DEFAULT 'manual',
          description TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      
      console.log('✅ Tabla event_types creada exitosamente');
    }

    // Verificar tipos de eventos existentes
    console.log('\n🔍 Verificando tipos de eventos existentes...');
    const existingEventTypes = await client.query('SELECT name FROM event_types');
    const existingNames = existingEventTypes.rows.map(row => row.name);
    
    console.log(`📊 Tipos de eventos existentes: ${existingNames.length}`);
    if (existingNames.length > 0) {
      console.log('   Existentes:', existingNames.join(', '));
    }

    // Contadores para el resumen
    let insertedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    console.log('\n🌱 Iniciando siembra de tipos de eventos...');
    
    // Insertar cada tipo de evento
    for (const eventType of eventTypesData) {
      try {
        // Verificar si ya existe
        if (existingNames.includes(eventType.name)) {
          console.log(`⚠️  Saltando '${eventType.name}' - ya existe`);
          skippedCount++;
          continue;
        }

        // Insertar nuevo tipo de evento
        await client.query(`
          INSERT INTO event_types (
            name, 
            auto_registration_default, 
            attendance_method_default, 
            description,
            created_at,
            updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6)
        `, [
          eventType.name,
          eventType.autoRegistrationDefault,
          eventType.attendanceMethodDefault,
          `Tipo de evento: ${eventType.name}`,
          new Date(),
          new Date()
        ]);
        
        console.log(`✅ Insertado: ${eventType.name}`);
        insertedCount++;
        
      } catch (error) {
        console.error(`❌ Error insertando '${eventType.name}':`, error.message);
        errorCount++;
      }
    }

    // Mostrar resumen final
    console.log('\n📊 RESUMEN DE SIEMBRA:');
    console.log(`   ✅ Insertados: ${insertedCount}`);
    console.log(`   ⚠️  Saltados: ${skippedCount}`);
    console.log(`   ❌ Errores: ${errorCount}`);
    console.log(`   📈 Total procesados: ${insertedCount + skippedCount + errorCount}`);

    // Verificar totales finales
    const finalCount = await client.query('SELECT COUNT(*) as count FROM event_types');
    console.log(`\n📊 TOTAL FINAL EN BASE DE DATOS: ${finalCount.rows[0].count} tipos de eventos`);

    if (insertedCount > 0) {
      console.log('\n🎉 Siembra de tipos de eventos completada exitosamente!');
    } else if (skippedCount === eventTypesData.length) {
      console.log('\n✅ Todos los tipos de eventos ya existían - no se requirió inserción');
    }

  } catch (error) {
    console.error('❌ Error durante la siembra de tipos de eventos:', error);
  } finally {
    await client.end();
    console.log('\n🔌 Conexión cerrada');
  }
}

// Ejecutar la función
seedEventTypes();