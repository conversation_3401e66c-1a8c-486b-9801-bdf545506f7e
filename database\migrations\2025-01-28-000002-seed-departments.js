/**
 * Migración de Strapi v5 para poblar departamentos
 * Fecha: 2025-01-28
 * Descripción: Carga inicial de departamentos que pueden invitar a eventos
 */

const fs = require('fs');
const path = require('path');

/**
 * Función para cargar datos de departamentos desde archivo JSON
 */
function loadDepartmentsData() {
  try {
    const dataPath = path.join(__dirname, '..', 'data', 'departments-data.json');
    const rawData = fs.readFileSync(dataPath, 'utf8');
    const data = JSON.parse(rawData);
    return data.departments;
  } catch (error) {
    console.error('Error al cargar datos de departamentos:', error.message);
    throw error;
  }
}

/**
 * Función para verificar si un departamento ya existe
 */
async function departmentExists(knex, name) {
  try {
    const existing = await knex('departments')
      .where({ name })
      .first();
    return existing;
  } catch (error) {
    console.error(`Error al verificar departamento ${name}:`, error.message);
    return null;
  }
}

/**
 * Función para insertar un departamento
 */
async function insertDepartment(knex, department) {
  try {
    const [inserted] = await knex('departments')
      .insert({
        name: department.name,
        description: department.description,
        is_active: department.isActive,
        created_at: new Date(),
        updated_at: new Date()
      })
      .returning(['id', 'name']);
    return inserted;
  } catch (error) {
    console.error(`Error al insertar departamento ${department.name}:`, error.message);
    throw error;
  }
}

module.exports = {
  /**
   * Migración hacia adelante (up)
   */
  async up(knex) {
    console.log('🚀 Iniciando migración de departamentos...');
    
    try {
      // Verificar si la tabla existe
      const tableExists = await knex.schema.hasTable('departments');
      
      if (!tableExists) {
        console.log('⚠️  La tabla departments no existe aún. Saltando siembra.');
        return;
      }
      
      // Cargar datos de departamentos
      const departments = loadDepartmentsData();
      console.log(`📋 Cargados ${departments.length} departamentos`);
      
      let insertedCount = 0;
      let skippedCount = 0;
      
      // Procesar cada departamento
      for (const department of departments) {
        console.log(`\n🔍 Procesando: ${department.name}`);
        
        // Verificar si el departamento ya existe
        const existing = await departmentExists(knex, department.name);
        
        if (existing) {
          console.log(`⚠️  Departamento ya existe: ${department.name} (ID: ${existing.id})`);
          skippedCount++;
          continue;
        }
        
        // Insertar nuevo departamento
        const inserted = await insertDepartment(knex, department);
        console.log(`✅ Departamento insertado: ${inserted.name} (ID: ${inserted.id})`);
        insertedCount++;
      }
      
      // Resumen
      console.log('\n📊 RESUMEN DE MIGRACIÓN:');
      console.log(`✅ Departamentos insertados: ${insertedCount}`);
      console.log(`⚠️  Departamentos omitidos: ${skippedCount}`);
      console.log(`📋 Total procesados: ${departments.length}`);
      
      console.log('🎉 Migración de departamentos completada!');
      
    } catch (error) {
      console.error('❌ Error en migración de departamentos:', error.message);
      throw error;
    }
  },

  /**
   * Migración hacia atrás (down)
   */
  async down(knex) {
    console.log('🔄 Revirtiendo migración de departamentos...');
    
    try {
      // Cargar datos para saber qué departamentos eliminar
      const departments = loadDepartmentsData();
      const departmentNames = departments.map(d => d.name);
      
      console.log(`🗑️  Eliminando ${departmentNames.length} departamentos...`);
      
      // Eliminar departamentos por nombre
      for (const name of departmentNames) {
        try {
          const existing = await departmentExists(knex, name);
          
          if (existing) {
            await knex('departments')
              .where({ id: existing.id })
              .del();
            console.log(`🗑️  Eliminado: ${name}`);
          } else {
            console.log(`⚠️  No encontrado: ${name}`);
          }
        } catch (error) {
          console.error(`❌ Error eliminando ${name}:`, error.message);
        }
      }
      
      console.log('✅ Reversión de migración completada');
      
    } catch (error) {
      console.error('❌ Error en reversión de migración:', error.message);
      throw error;
    }
  }
};