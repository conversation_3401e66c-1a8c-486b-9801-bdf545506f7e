/**
 * Rutas para el modelo Department
 * Define las rutas personalizadas y por defecto para departamentos
 */

import { factories } from '@strapi/strapi';

// Crear el router por defecto para el modelo department
const departmentDefaultRouter = factories.createCoreRouter("api::department.department");

/**
 * Función para crear un router personalizado que combina rutas por defecto con rutas adicionales
 * @param {Object} innerRouter - Router por defecto de Strapi
 * @param {Array} routeOverride - Rutas que sobrescriben las por defecto
 * @param {Array} extraRoutes - Rutas adicionales personalizadas
 */

const departmentCustomRouter = (innerRouter, routeOveride = [], extraRoutes = []) => {
    let routes;
    return {
      get prefix() {
        return innerRouter.prefix;
      },
      get routes() {
        if (!routes) routes = innerRouter.routes;
  
        const newRoutes = routes.map((route) => {
          let found = false;
  
          routeOveride.forEach((overide) => {
            if (
              route.handler === overide.handler &&
              route.method === overide.method
            ) {
              found = overide;
            }
          });
  
          return found || route;
  
        });
        
        return newRoutes.concat(extraRoutes);
      },
    };
  };
  
// Rutas adicionales personalizadas
const extraRoutes = [
  {
    method: 'GET',
    path: '/departments/active/active',
    handler: 'department.getActive',
    config: {
      auth: false, // Cambiar según necesidades de autenticación
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'GET',
    path: '/departments/:id/stats',
    handler: 'department.getEventStats',
    config: {
      auth: false, // Cambiar según necesidades de autenticación
      policies: [],
      middlewares: [],
    },
  }
];

// Exportar el router personalizado
export default departmentCustomRouter(departmentDefaultRouter, [], extraRoutes);