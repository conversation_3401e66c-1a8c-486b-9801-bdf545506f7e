'use strict';

const { sanitize } = require('@strapi/utils');

module.exports = {
  // PUT /api/account/profile
  async updateProfile(ctx) {
    const { id } = ctx.state.user;
    const { body } = ctx.request;

    const allowedFields = ['firstName', 'lastName', 'phone'];
    const updateData = Object.keys(body)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = body[key];
        return obj;
      }, {});

    const updatedUser = await strapi.entityService.update('plugin::users-permissions.user', id, {
      data: updateData,
      populate: ['avatar'],
    });

    const sanitizedUser = await sanitize.contentAPI.output(updatedUser, strapi.getModel('plugin::users-permissions.user'));

    ctx.body = sanitizedUser;
  },

  // PUT /api/account/password
  async changePassword(ctx) {
    const { id } = ctx.state.user;
    const { currentPassword, password, passwordConfirmation } = ctx.request.body;

    if (password !== passwordConfirmation) {
      return ctx.badRequest('Las contraseñas no coinciden.');
    }

    const userService = strapi.plugin('users-permissions').service('user');
    const user = await userService.fetch(id);

    const validPassword = await userService.validatePassword(currentPassword, user.password);

    if (!validPassword) {
      return ctx.badRequest('La contraseña actual es incorrecta.');
    }

    await userService.edit(id, { password });

    ctx.body = { message: 'Contraseña actualizada correctamente.' };
  }
};
