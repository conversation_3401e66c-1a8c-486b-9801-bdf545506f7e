import type { Schema, Struct } from '@strapi/strapi';

export interface EventEventType extends Struct.ComponentSchema {
  collectionName: 'components_event_event_types';
  info: {
    description: 'Event type configuration with default settings';
    displayName: 'event-type';
  };
  attributes: {
    attendanceMethodDefault: Schema.Attribute.Enumeration<
      ['manual', 'kiosco_rapido']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'manual'>;
    autoRegistrationDefault: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    name: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface SessionAttendanceSession extends Struct.ComponentSchema {
  collectionName: 'components_session_attendance_sessions';
  info: {
    description: 'Session attendance details';
    displayName: 'attendance-session';
  };
  attributes: {
    attendanceMode: Schema.Attribute.Enumeration<['normal', 'kiosk']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'normal'>;
    comment: Schema.Attribute.Text;
    date: Schema.Attribute.Date & Schema.Attribute.Required;
    isDefault: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<false>;
    sessionId: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    status: Schema.Attribute.Enumeration<
      ['pendiente', 'en_progreso', 'completada', 'cancelada']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'pendiente'>;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'event.event-type': EventEventType;
      'session.attendance-session': SessionAttendanceSession;
    }
  }
}
