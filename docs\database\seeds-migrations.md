# 🌱 Sistema de Seeds y Migraciones - SCMP

## Sistema de Competencia y Ministerio Pastoral

Este documento describe el sistema de seeds y migraciones implementado para el proyecto SCMP en Strapi v5. El sistema permite cargar datos iniciales estructurados de manera automática y confiable.

## Arquitectura del Sistema

### Componentes Principales

1. **Archivo de Migración**: `database/migrations/2025-01-28-000000-seed-initial-data.js`
2. **Datos Iniciales**: `database/seeds/initial-data.json`
3. **Script de Gestión**: `scripts/seed-manager.js`

### Flujo de Ejecución

```mermaid
graph TD
    A[Inicio de Strapi] --> B[Detección de Migraciones]
    B --> C[Ejecución de Migración]
    C --> D[Lectura de initial-data.json]
    D --> E[Inserción de Fields]
    E --> F[Inserción de Zones]
    F --> G[Inserción de Districts]
    G --> H[Inserción de Churches]
    H --> I[Creación de Relaciones]
    I --> J[Migración Completada]
```

## Estructura de Datos

### Jerarquía Organizacional

```
Field (Campo/Asociación)
└── Zone (Zona)
    └── District (Distrito)
        └── Church (Iglesia)
```

### Esquema de Relaciones

- **Field → Zone**: Relación `oneToMany`
- **Zone → District**: Relación `oneToMany`
- **District → Church**: Relación `oneToMany`

## Archivo de Migración

### Ubicación
```
database/migrations/2025-01-28-000000-seed-initial-data.js
```

### Características Principales

- **Ejecución Automática**: Se ejecuta al iniciar Strapi
- **Idempotente**: Puede ejecutarse múltiples veces sin duplicar datos
- **Transaccional**: Si falla, se revierten todos los cambios
- **Manejo de Relaciones**: Utiliza tablas de enlace para relaciones

### Estructura del Código

```javascript
// Función principal de migración
async function up(knex) {
  // 1. Lectura de datos desde JSON
  // 2. Validación de estructura
  // 3. Inserción secuencial:
  //    - Fields
  //    - Zones (con relaciones a Fields)
  //    - Districts (con relaciones a Zones)
  //    - Churches (con relaciones a Districts)
  // 4. Logging de progreso
}
```

## Datos Iniciales

### Archivo JSON
```
database/seeds/initial-data.json
```

### Estructura

```json
{
  "fields": [
    {
      "documentId": "uuid",
      "name": "Nombre del Campo",
      "acronym": "ACR",
      "type": "association",
      "publishedAt": "ISO Date",
      "locale": "es"
    }
  ],
  "zones": [
    {
      "documentId": "uuid",
      "name": "Nombre de la Zona",
      "field_name": "Nombre del Campo Padre"
    }
  ],
  "districts": [...],
  "churches": [...]
}
```

### Estadísticas de Datos

- **Fields**: 4 asociaciones
- **Zones**: 27 zonas
- **Districts**: 181 distritos
- **Churches**: 1,075 iglesias

## Manejo de Relaciones en Strapi v5

### Tablas de Enlace

Strapi v5 utiliza tablas de enlace para manejar relaciones:

- `zones_field_lnk`: Relaciona zones con fields
- `districts_zone_lnk`: Relaciona districts con zones
- `churches_district_lnk`: Relaciona churches con districts

### Estructura de Tablas de Enlace

```sql
-- Ejemplo: zones_field_lnk
CREATE TABLE zones_field_lnk (
  id INTEGER PRIMARY KEY,
  zone_id INTEGER REFERENCES zones(id),
  field_id INTEGER REFERENCES fields(id)
);
```

## Script de Gestión

### Ubicación
```
scripts/seed-manager.js
```

### Funcionalidades

- **Validación**: Verifica la estructura del archivo JSON
- **Información**: Muestra estadísticas de los datos
- **Ayuda**: Proporciona información sobre el sistema

### Comandos Disponibles

```bash
# Validar estructura de datos
node scripts/seed-manager.js validate

# Mostrar información del sistema
node scripts/seed-manager.js info

# Mostrar ayuda
node scripts/seed-manager.js help
```

## Proceso de Ejecución

### Automático (Recomendado)

1. **Inicio de Strapi**: `npm run dev`
2. **Detección Automática**: Strapi detecta archivos de migración
3. **Ejecución**: Se ejecuta la migración automáticamente
4. **Logging**: Se muestran mensajes de progreso en consola

### Logs de Ejemplo

```
[info]: [internal migration]: migrating 2025-01-28-000000-seed-initial-data.js
🌱 Iniciando carga de datos iniciales...
📊 Datos a procesar:
   - Fields: 4
   - Zones: 27
   - Districts: 181
   - Churches: 1075
📝 Insertando Fields...
   ✅ Field insertado: Asociación Central Dominicana
   ✅ Field insertado: Asociación Dominicana del Norte
   ...
✅ Migración completada exitosamente
```

## Características Técnicas

### Idempotencia

- **Verificación de Existencia**: Antes de insertar, verifica si el registro ya existe
- **Actualización Condicional**: Solo actualiza si hay cambios
- **Prevención de Duplicados**: Evita la creación de registros duplicados

### Manejo de Errores

- **Transacciones**: Uso de transacciones de base de datos
- **Rollback Automático**: Si falla una operación, se revierten todos los cambios
- **Logging Detallado**: Mensajes informativos sobre errores

### Performance

- **Inserción en Lotes**: Optimizada para grandes volúmenes de datos
- **Índices**: Utiliza índices de base de datos para búsquedas rápidas
- **Memoria Eficiente**: Procesamiento secuencial para evitar sobrecarga

## Solución de Problemas

### Errores Comunes

#### 1. Error de Columna No Encontrada
```
MigrationError: column "field_name" does not exist
```
**Solución**: Verificar que se usen las tablas de enlace correctas.

#### 2. Datos Duplicados
```
Error: duplicate key value violates unique constraint
```
**Solución**: La migración es idempotente, este error no debería ocurrir.

#### 3. Archivo JSON Malformado
```
SyntaxError: Unexpected token in JSON
```
**Solución**: Validar el archivo JSON con `node scripts/seed-manager.js validate`.

### Debugging

1. **Verificar Logs**: Revisar los logs de Strapi durante el inicio
2. **Validar Datos**: Usar el script de validación
3. **Verificar Base de Datos**: Comprobar que las tablas existen

## Mejores Prácticas

### Desarrollo

1. **Backup**: Siempre hacer backup antes de ejecutar migraciones
2. **Validación**: Validar datos antes de la migración
3. **Testing**: Probar en entorno de desarrollo primero
4. **Versionado**: Mantener versiones de los archivos de datos

### Producción

1. **Monitoreo**: Supervisar la ejecución de migraciones
2. **Rollback Plan**: Tener un plan de rollback preparado
3. **Documentación**: Documentar todos los cambios
4. **Comunicación**: Informar al equipo sobre migraciones

## Mantenimiento

### Actualización de Datos

1. **Modificar JSON**: Actualizar `initial-data.json`
2. **Nueva Migración**: Crear nueva migración si es necesario
3. **Validar**: Usar script de validación
4. **Probar**: Ejecutar en entorno de desarrollo

### Monitoreo

- **Logs de Aplicación**: Revisar logs regularmente
- **Performance**: Monitorear tiempo de ejecución
- **Integridad**: Verificar integridad de datos periódicamente

## Referencias

- [Documentación de Migraciones Strapi v5](https://docs.strapi.io/cms/database-migrations)
- [Guía de Relaciones en Strapi](https://docs.strapi.io/dev-docs/backend-customization/models#relations)
- [Knex.js Documentation](http://knexjs.org/)

## Changelog

### v1.0.0 (2025-01-28)
- ✅ Implementación inicial del sistema de seeds
- ✅ Migración automática de datos iniciales
- ✅ Soporte para relaciones jerárquicas
- ✅ Script de gestión y validación
- ✅ Documentación completa