{"version": "0.0.0", "keywords": [], "type": "commonjs", "exports": {"./package.json": "./package.json", "./strapi-server": {"types": "./dist/server/src/index.d.ts", "source": "./server/src/index.ts", "import": "./dist/server/index.mjs", "require": "./dist/server/index.js", "default": "./dist/server/index.js"}, "./strapi-admin": {"types": "./dist/admin/src/index.d.ts", "source": "./admin/src/index.ts", "import": "./dist/admin/index.mjs", "require": "./dist/admin/index.js", "default": "./dist/admin/index.js"}}, "files": ["dist"], "scripts": {"build": "strapi-plugin build", "watch": "strapi-plugin watch", "watch:link": "strapi-plugin watch:link", "verify": "strapi-plugin verify", "test:ts:back": "run -T tsc -p server/tsconfig.json"}, "dependencies": {}, "devDependencies": {"@strapi/strapi": "^5.12.6", "@strapi/sdk-plugin": "^5.3.2", "prettier": "^3.5.3", "@strapi/typescript-utils": "^5.12.6", "typescript": "^5.8.3"}, "peerDependencies": {"@strapi/strapi": "^5.12.6", "@strapi/sdk-plugin": "^5.3.2"}, "strapi": {"kind": "plugin", "name": "custom-permissions", "displayName": "", "description": ""}, "name": "custom-permissions", "description": "", "license": "MIT", "author": "MmichelL <<EMAIL>>"}