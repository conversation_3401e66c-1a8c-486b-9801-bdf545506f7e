import type { Core } from '@strapi/strapi';

const controller = ({ strapi }: { strapi: Core.Strapi }) => ({
  index(ctx) {
    ctx.body = strapi
      .plugin('custom-permissions')
      // the name of the service file & the method.
      .service('service')
      .getWelcomeMessage();
  },
  async home(ctx) {
    ctx.body = { message: 'Home endpoint from custom-permissions plugin' };
  },
  async dashboard(ctx) {
    ctx.body = { message: 'Dashboard endpoint from custom-permissions plugin' };
  },
});

export default controller;