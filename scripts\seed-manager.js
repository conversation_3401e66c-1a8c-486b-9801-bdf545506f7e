#!/usr/bin/env node

/**
 * Script de utilidad para gestionar las semillas de datos iniciales
 * Uso: node scripts/seed-manager.js [comando]
 * 
 * Comandos disponibles:
 * - load: Cargar datos iniciales
 * - reset: Eliminar y recargar datos
 * - validate: Validar estructura del JSON
 * - help: Mostrar ayuda
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Función para imprimir con colores
function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Función para validar la estructura del JSON
function validateSeedData() {
try {
    const seedPath = path.join(__dirname, '..', 'database', 'seeds', 'initial-data.json');
    
    if (!fs.existsSync(seedPath)) {
      colorLog('❌ Error: No se encontró el archivo initial-data.json', 'red');
      return false;
    }

    const seedData = JSON.parse(fs.readFileSync(seedPath, 'utf8'));
    
    // Validar estructura básica
    const requiredKeys = ['fields', 'zones', 'districts', 'churches'];
    for (const key of requiredKeys) {
      if (!seedData[key] || !Array.isArray(seedData[key])) {
        colorLog(`❌ Error: Falta la clave '${key}' o no es un array`, 'red');
        return false;
      }
    }

    // Validar fields - Los fields deben ser únicos globalmente
    const fieldNames = new Set();
    for (const field of seedData.fields) {
      if (!field.name) {
        colorLog('❌ Error: Field sin nombre encontrado', 'red');
        return false;
      }
      if (fieldNames.has(field.name)) {
        colorLog(`❌ Error: Field duplicado: ${field.name}`, 'red');
        return false;
      }
      fieldNames.add(field.name);
    }

    // Validar zones - Las zones deben ser únicas globalmente
    const zoneNames = new Set();
    for (const zone of seedData.zones) {
      if (!zone.name || !zone.field_name) {
        colorLog('❌ Error: Zone sin nombre o field_name', 'red');
        return false;
      }
      if (!fieldNames.has(zone.field_name)) {
        colorLog(`❌ Error: Zone '${zone.name}' referencia field inexistente: ${zone.field_name}`, 'red');
        return false;
      }
      if (zoneNames.has(zone.name)) {
        colorLog(`❌ Error: Zone duplicada: ${zone.name}`, 'red');
        return false;
      }
      zoneNames.add(zone.name);
    }

    // Validar districts - Los districts pueden tener el mismo nombre si están en zonas diferentes
    const districtsByZone = new Map(); // Mapa: zona -> Set de nombres de distritos
    const districtNames = new Set(); // Para verificar referencias
    
    for (const district of seedData.districts) {
      if (!district.name || !district.zone_name) {
        colorLog('❌ Error: District sin nombre o zone_name', 'red');
        return false;
      }
      if (!zoneNames.has(district.zone_name)) {
        colorLog(`❌ Error: District '${district.name}' referencia zone inexistente: ${district.zone_name}`, 'red');
        return false;
      }
      
      // Verificar duplicados dentro de la misma zona
      if (!districtsByZone.has(district.zone_name)) {
        districtsByZone.set(district.zone_name, new Set());
      }
      
      const districtsInZone = districtsByZone.get(district.zone_name);
      if (districtsInZone.has(district.name)) {
        colorLog(`❌ Error: District duplicado en la misma zona '${district.zone_name}': ${district.name}`, 'red');
        return false;
      }
      
      districtsInZone.add(district.name);
      districtNames.add(district.name); // Para referencias de iglesias
    }

    // Validar churches - Las churches pueden tener el mismo nombre si están en distritos diferentes
    const churchesByDistrict = new Map(); // Mapa: distrito -> Set de nombres de iglesias
    
    for (const church of seedData.churches) {
      if (!church.name || !church.district_name) {
        colorLog('❌ Error: Church sin nombre o district_name', 'red');
        return false;
      }
      if (!districtNames.has(church.district_name)) {
        colorLog(`❌ Error: Church '${church.name}' referencia district inexistente: ${church.district_name}`, 'red');
        return false;
      }
      
      // Verificar duplicados dentro del mismo distrito
      if (!churchesByDistrict.has(church.district_name)) {
        churchesByDistrict.set(church.district_name, new Set());
      }
      
      const churchesInDistrict = churchesByDistrict.get(church.district_name);
      if (churchesInDistrict.has(church.name)) {
        colorLog(`❌ Error: Church duplicada en el mismo distrito '${church.district_name}': ${church.name}`, 'red');
        return false;
      }
      
      churchesInDistrict.add(church.name);
    }

    // Mostrar estadísticas
    colorLog('\n📊 Estadísticas de validación:', 'cyan');
    colorLog(`   Fields: ${seedData.fields.length}`, 'blue');
    colorLog(`   Zones: ${seedData.zones.length}`, 'blue');
    colorLog(`   Districts: ${seedData.districts.length}`, 'blue');
    colorLog(`   Churches: ${seedData.churches.length}`, 'blue');
    
    // Mostrar estadísticas de duplicados permitidos
    const totalDistrictNames = new Set(seedData.districts.map(d => d.name)).size;
    const totalChurchNames = new Set(seedData.churches.map(c => c.name)).size;
    
    if (seedData.districts.length > totalDistrictNames) {
      colorLog(`   Districts con nombres reutilizados: ${seedData.districts.length - totalDistrictNames}`, 'yellow');
    }
    if (seedData.churches.length > totalChurchNames) {
      colorLog(`   Churches con nombres reutilizados: ${seedData.churches.length - totalChurchNames}`, 'yellow');
    }
    
    colorLog('\n✅ Validación exitosa: La estructura del JSON es correcta', 'green');
    
    return true;
  } catch (error) {
    colorLog(`❌ Error al validar: ${error.message}`, 'red');
    return false;
  }
}

// Función informativa sobre la carga de seeds
function showLoadInfo() {
  colorLog('ℹ️ Información sobre carga de seeds:', 'cyan');
  colorLog('');
  colorLog('En Strapi v5, las migraciones se ejecutan automáticamente al iniciar la aplicación.', 'blue');
  colorLog('Para cargar las seeds:', 'blue');
  colorLog('');
  colorLog('1. Primero valida los datos:', 'bright');
  colorLog('   npm run seeds:validate', 'green');
  colorLog('');
  colorLog('2. Si la validación es exitosa, inicia Strapi:', 'bright');
  colorLog('   npm run dev', 'green');
  colorLog('');
  colorLog('Las seeds se cargarán automáticamente durante el inicio de la aplicación.', 'yellow');
  colorLog('');
}

// Función para mostrar ayuda
function showHelp() {
  colorLog('\n🌱 Gestor de Seeds para Strapi v5', 'bright');
  colorLog('=====================================\n', 'bright');
  
  colorLog('Comandos disponibles:', 'cyan');
  colorLog('');
  
  colorLog('📋 validate', 'yellow');
  colorLog('   Valida la estructura y consistencia de los datos en initial-data.json', 'white');
  colorLog('   Verifica duplicados, referencias y formato JSON', 'gray');
  colorLog('');
  
  colorLog('ℹ️ load', 'yellow');
  colorLog('   Muestra información sobre cómo cargar las seeds', 'white');
  colorLog('   En Strapi v5, las migraciones se ejecutan automáticamente', 'gray');
  colorLog('');
  
  colorLog('❓ help', 'yellow');
  colorLog('   Muestra esta ayuda', 'white');
  colorLog('');
  
  colorLog('Ejemplos de uso:', 'cyan');
  colorLog('  npm run seeds:validate', 'green');
  colorLog('  npm run seeds:help', 'green');
  colorLog('');
  
  colorLog('📁 Archivos importantes:', 'cyan');
  colorLog('  • database/seeds/initial-data.json - Datos de semilla', 'white');
  colorLog('  • database/migrations/2025-01-28-000000-seed-initial-data.ts - Migración', 'white');
  colorLog('');
  
  colorLog('🚀 Para cargar las seeds:', 'cyan');
  colorLog('  1. npm run seeds:validate', 'green');
  colorLog('  2. npm run dev (las seeds se cargan automáticamente)', 'green');
  colorLog('');
}

// Función principal
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'load':
      showLoadInfo();
      break;
    case 'validate':
      validateSeedData();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      colorLog('❌ Comando no reconocido. Usa "help" para ver los comandos disponibles.', 'red');
      showHelp();
      process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}

module.exports = {
  validateSeedData,
  showLoadInfo,
  showHelp
};

// Ejecutar si se llama directamente
if (require.main === module) {
  main();
}