const { Client } = require('pg');
require('dotenv').config();

// Configuración de la base de datos con credenciales de Supabase
const client = new Client({
  host: 'aws-0-us-east-2.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.ktykxlboodjvnlabbwoy',
  password: 'GranCH@1844',
  ssl: {
    rejectUnauthorized: false // Deshabilitar verificación SSL estricta
  }
});

async function checkAndRemoveDuplicates() {
  try {
    console.log('🔗 Conectando a la base de datos...');
    await client.connect();
    console.log('✅ Conectado exitosamente');

    // 1. Verificar duplicados en Zones (mismo nombre en el mismo campo)
    console.log('\n🔍 Verificando duplicados en Zones...');
    const duplicateZones = await client.query(`
      SELECT z1.name, f.name as field_name, COUNT(*) as count
      FROM zones z1
      JOIN zones_field_lnk zfl ON z1.id = zfl.zone_id
      JOIN fields f ON zfl.field_id = f.id
      GROUP BY z1.name, f.name
      HAVING COUNT(*) > 1
    `);

    if (duplicateZones.rows.length > 0) {
      console.log(`❌ Encontrados ${duplicateZones.rows.length} grupos de zones duplicadas:`);
      for (const duplicate of duplicateZones.rows) {
        console.log(`   - Zone '${duplicate.name}' en Field '${duplicate.field_name}': ${duplicate.count} duplicados`);
        
        // Obtener todas las zones duplicadas para este grupo
        const zonesToDelete = await client.query(`
          SELECT z.id, z.name
          FROM zones z
          JOIN zones_field_lnk zfl ON z.id = zfl.zone_id
          JOIN fields f ON zfl.field_id = f.id
          WHERE z.name = $1 AND f.name = $2
          ORDER BY z.id
        `, [duplicate.name, duplicate.field_name]);
        
        // Eliminar todos excepto el primero (mantener el de menor ID)
        for (let i = 1; i < zonesToDelete.rows.length; i++) {
          const zoneToDelete = zonesToDelete.rows[i];
          
          // Eliminar relaciones primero
          await client.query('DELETE FROM zones_field_lnk WHERE zone_id = $1', [zoneToDelete.id]);
          await client.query('DELETE FROM districts_zone_lnk WHERE zone_id = $1', [zoneToDelete.id]);
          
          // Eliminar la zone
          await client.query('DELETE FROM zones WHERE id = $1', [zoneToDelete.id]);
          console.log(`     ✅ Eliminada zone duplicada ID ${zoneToDelete.id}: '${zoneToDelete.name}'`);
        }
      }
    } else {
      console.log('✅ No se encontraron zones duplicadas');
    }

    // 2. Verificar duplicados en Districts (mismo nombre en la misma zona del mismo campo)
    console.log('\n🔍 Verificando duplicados en Districts...');
    const duplicateDistricts = await client.query(`
      SELECT d.name, z.name as zone_name, f.name as field_name, COUNT(*) as count
      FROM districts d
      JOIN districts_zone_lnk dzl ON d.id = dzl.district_id
      JOIN zones z ON dzl.zone_id = z.id
      JOIN zones_field_lnk zfl ON z.id = zfl.zone_id
      JOIN fields f ON zfl.field_id = f.id
      GROUP BY d.name, z.name, f.name
      HAVING COUNT(*) > 1
    `);

    if (duplicateDistricts.rows.length > 0) {
      console.log(`❌ Encontrados ${duplicateDistricts.rows.length} grupos de districts duplicados:`);
      for (const duplicate of duplicateDistricts.rows) {
        console.log(`   - District '${duplicate.name}' en Zone '${duplicate.zone_name}' del Field '${duplicate.field_name}': ${duplicate.count} duplicados`);
        
        // Obtener todos los districts duplicados para este grupo
        const districtsToDelete = await client.query(`
          SELECT d.id, d.name
          FROM districts d
          JOIN districts_zone_lnk dzl ON d.id = dzl.district_id
          JOIN zones z ON dzl.zone_id = z.id
          JOIN zones_field_lnk zfl ON z.id = zfl.zone_id
          JOIN fields f ON zfl.field_id = f.id
          WHERE d.name = $1 AND z.name = $2 AND f.name = $3
          ORDER BY d.id
        `, [duplicate.name, duplicate.zone_name, duplicate.field_name]);
        
        // Eliminar todos excepto el primero (mantener el de menor ID)
        for (let i = 1; i < districtsToDelete.rows.length; i++) {
          const districtToDelete = districtsToDelete.rows[i];
          
          // Eliminar relaciones primero
          await client.query('DELETE FROM districts_zone_lnk WHERE district_id = $1', [districtToDelete.id]);
          await client.query('DELETE FROM churches_district_lnk WHERE district_id = $1', [districtToDelete.id]);
          
          // Eliminar el district
          await client.query('DELETE FROM districts WHERE id = $1', [districtToDelete.id]);
          console.log(`     ✅ Eliminado district duplicado ID ${districtToDelete.id}: '${districtToDelete.name}'`);
        }
      }
    } else {
      console.log('✅ No se encontraron districts duplicados');
    }

    // 3. Verificar duplicados en Churches (mismo nombre en el mismo distrito)
    console.log('\n🔍 Verificando duplicados en Churches...');
    const duplicateChurches = await client.query(`
      SELECT c.name, d.name as district_name, z.name as zone_name, f.name as field_name, COUNT(*) as count
      FROM churches c
      JOIN churches_district_lnk cdl ON c.id = cdl.church_id
      JOIN districts d ON cdl.district_id = d.id
      JOIN districts_zone_lnk dzl ON d.id = dzl.district_id
      JOIN zones z ON dzl.zone_id = z.id
      JOIN zones_field_lnk zfl ON z.id = zfl.zone_id
      JOIN fields f ON zfl.field_id = f.id
      GROUP BY c.name, d.name, z.name, f.name
      HAVING COUNT(*) > 1
    `);

    if (duplicateChurches.rows.length > 0) {
      console.log(`❌ Encontrados ${duplicateChurches.rows.length} grupos de churches duplicadas:`);
      for (const duplicate of duplicateChurches.rows) {
        console.log(`   - Church '${duplicate.name}' en District '${duplicate.district_name}' de Zone '${duplicate.zone_name}' del Field '${duplicate.field_name}': ${duplicate.count} duplicados`);
        
        // Obtener todas las churches duplicadas para este grupo
        const churchesToDelete = await client.query(`
          SELECT c.id, c.name
          FROM churches c
          JOIN churches_district_lnk cdl ON c.id = cdl.church_id
          JOIN districts d ON cdl.district_id = d.id
          JOIN districts_zone_lnk dzl ON d.id = dzl.district_id
          JOIN zones z ON dzl.zone_id = z.id
          JOIN zones_field_lnk zfl ON z.id = zfl.zone_id
          JOIN fields f ON zfl.field_id = f.id
          WHERE c.name = $1 AND d.name = $2 AND z.name = $3 AND f.name = $4
          ORDER BY c.id
        `, [duplicate.name, duplicate.district_name, duplicate.zone_name, duplicate.field_name]);
        
        // Eliminar todos excepto el primero (mantener el de menor ID)
        for (let i = 1; i < churchesToDelete.rows.length; i++) {
          const churchToDelete = churchesToDelete.rows[i];
          
          // Eliminar relaciones primero
          await client.query('DELETE FROM churches_district_lnk WHERE church_id = $1', [churchToDelete.id]);
          
          // Eliminar la church
          await client.query('DELETE FROM churches WHERE id = $1', [churchToDelete.id]);
          console.log(`     ✅ Eliminada church duplicada ID ${churchToDelete.id}: '${churchToDelete.name}'`);
        }
      }
    } else {
      console.log('✅ No se encontraron churches duplicadas');
    }

    // 4. Mostrar resumen final
    console.log('\n📊 RESUMEN FINAL DESPUÉS DE LIMPIEZA:');
    const finalCounts = await Promise.all([
      client.query('SELECT COUNT(*) as count FROM fields'),
      client.query('SELECT COUNT(*) as count FROM zones'),
      client.query('SELECT COUNT(*) as count FROM districts'),
      client.query('SELECT COUNT(*) as count FROM churches')
    ]);

    console.log(`   - Fields: ${finalCounts[0].rows[0].count}`);
    console.log(`   - Zones: ${finalCounts[1].rows[0].count}`);
    console.log(`   - Districts: ${finalCounts[2].rows[0].count}`);
    console.log(`   - Churches: ${finalCounts[3].rows[0].count}`);

    console.log('\n✅ Verificación y limpieza de duplicados completada');

  } catch (error) {
    console.error('❌ Error durante la verificación de duplicados:', error);
  } finally {
    await client.end();
    console.log('🔌 Conexión cerrada');
  }
}

// Ejecutar la función
checkAndRemoveDuplicates();