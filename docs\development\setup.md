# 🔧 Guía de Configuración y Desarrollo - SCMP

## Sistema de Competencia y Ministerio Pastoral

Esta guía te ayudará a configurar y desarrollar el **Sistema de Competencia y Ministerio Pastoral (SCMP)**

## Requisitos del Sistema

### Software Requerido

- **Node.js**: v18.0.0 o superior
- **npm**: v8.0.0 o superior
- **Git**: Para control de versiones
- **Editor de Código**: VS Code recomendado

### Sistemas Operativos Soportados

- Windows 10/11
- macOS 10.15+
- Ubuntu 18.04+
- Otras distribuciones Linux

## Configuración Inicial

### 1. Clonar el Repositorio

```bash
git clone <repository-url>
cd scmp-cm
```

### 2. Instalación de Dependencias

```bash
# Instalar dependencias del proyecto
npm install

# Verificar instalación
npm list --depth=0
```

### 3. Configuración de Variables de Entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar variables de entorno
nano .env
```

#### Variables de Entorno Principales

```env
# Configuración de la aplicación
HOST=0.0.0.0
PORT=1337
APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt
ADMIN_JWT_SECRET=your-admin-jwt-secret
TRANSFER_TOKEN_SALT=your-transfer-token-salt
JWT_SECRET=your-jwt-secret

# Base de datos (desarrollo)
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db

# Base de datos (producción)
# DATABASE_CLIENT=postgres
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=scmp_db
# DATABASE_USERNAME=postgres
# DATABASE_PASSWORD=password
# DATABASE_SSL=false
```

### 4. Inicialización de la Base de Datos

```bash
# Construir el proyecto
npm run build

# Iniciar en modo desarrollo (ejecuta migraciones automáticamente)
npm run dev
```

## Estructura del Proyecto

```
scmp-cm/
├── .env                     # Variables de entorno
├── .env.example            # Ejemplo de variables de entorno
├── package.json            # Dependencias y scripts
├── README.md              # Documentación principal
├── 
├── config/                # Configuraciones de Strapi
│   ├── admin.js
│   ├── api.js
│   ├── database.js
│   ├── middlewares.js
│   ├── plugins.js
│   └── server.js
├── 
├── database/              # Migraciones y seeds
│   ├── migrations/
│   │   └── 2025-01-28-000000-seed-initial-data.js
│   └── seeds/
│       └── initial-data.json
├── 
├── docs/                  # Documentación del proyecto
│   ├── README.md
│   ├── database/
│   │   ├── architecture.md
│   │   └── seeds-migrations.md
│   ├── development/
│   │   ├── setup.md
│   │   └── contributing.md
│   ├── api/
│   │   └── reference.md
│   ├── deployment/
│   │   └── guide.md
│   └── security/
│       └── guidelines.md
├── 
├── scripts/               # Scripts de utilidad
│   └── seed-manager.js
├── 
├── src/                   # Código fuente
│   ├── admin/            # Personalización del panel admin
│   ├── api/              # APIs y tipos de contenido
│   │   ├── church/
│   │   ├── district/
│   │   ├── field/
│   │   └── zone/
│   ├── extensions/       # Extensiones de Strapi
│   ├── middlewares/      # Middlewares personalizados
│   └── plugins/          # Plugins personalizados
├── 
├── public/               # Archivos públicos
└── .tmp/                 # Archivos temporales (SQLite, uploads)
```

## Scripts Disponibles

### Scripts de Desarrollo

```bash
# Iniciar en modo desarrollo
npm run dev

# Construir para producción
npm run build

# Iniciar en modo producción
npm start

# Limpiar archivos de construcción
npm run clean
```

### Scripts de Base de Datos

```bash
# Validar estructura de datos iniciales
node scripts/seed-manager.js validate

# Mostrar información del sistema de seeds
node scripts/seed-manager.js info

# Mostrar ayuda del seed manager
node scripts/seed-manager.js help
```

### Scripts de Calidad de Código

```bash
# Ejecutar linter
npm run lint

# Corregir problemas de linting automáticamente
npm run lint:fix

# Ejecutar tests
npm test

# Ejecutar tests en modo watch
npm run test:watch
```

## Configuración del Editor

### VS Code (Recomendado)

#### Extensiones Recomendadas

```json
// .vscode/extensions.json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### Configuración del Workspace

```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.exclude": {
    "node_modules": true,
    ".tmp": true,
    "dist": true,
    "build": true
  }
}
```

## Flujo de Desarrollo

### 1. Configuración Inicial

```bash
# Clonar y configurar
git clone <repository-url>
cd scmp-cm
npm install
cp .env.example .env
# Editar .env con tus configuraciones
```

### 2. Desarrollo Diario

```bash
# Iniciar servidor de desarrollo
npm run dev

# En otra terminal, ejecutar tests
npm run test:watch
```

### 3. Antes de Commit

```bash
# Verificar calidad de código
npm run lint
npm test

# Construir para verificar que no hay errores
npm run build
```

## APIs y Tipos de Contenido

### Endpoints Principales

```
GET    /api/fields          # Listar campos
GET    /api/fields/:id      # Obtener campo específico
POST   /api/fields          # Crear campo
PUT    /api/fields/:id      # Actualizar campo
DELETE /api/fields/:id      # Eliminar campo

GET    /api/zones           # Listar zonas
GET    /api/zones/:id       # Obtener zona específica

GET    /api/districts       # Listar distritos
GET    /api/districts/:id   # Obtener distrito específico

GET    /api/churches        # Listar iglesias
GET    /api/churches/:id    # Obtener iglesia específica
```

### Parámetros de Consulta

```bash
# Paginación
GET /api/churches?pagination[page]=1&pagination[pageSize]=25

# Filtros
GET /api/churches?filters[name][$contains]=Central

# Población de relaciones
GET /api/churches?populate=district.zone.field

# Ordenamiento
GET /api/churches?sort=name:asc
```

## Base de Datos

### Desarrollo (SQLite)

- **Archivo**: `.tmp/data.db`
- **Ventajas**: Fácil configuración, no requiere servidor
- **Desventajas**: Limitaciones de concurrencia

### Producción (PostgreSQL)

```bash
# Instalar PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Crear base de datos
sudo -u postgres createdb scmp_db

# Configurar variables de entorno
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=scmp_db
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your_password
```

## Debugging

### Logs de Desarrollo

```bash
# Logs detallados
DEBUG=strapi:* npm run dev

# Logs específicos de base de datos
DEBUG=strapi:database npm run dev

# Logs de migraciones
DEBUG=strapi:database:migration npm run dev
```

### Herramientas de Debug

1. **Strapi Admin Panel**: `http://localhost:1337/admin`
2. **API Documentation**: `http://localhost:1337/documentation`
3. **Database Browser**: Para SQLite, usar DB Browser for SQLite

## Solución de Problemas

### Problemas Comunes

#### 1. Error de Puerto en Uso

```bash
# Encontrar proceso usando el puerto
lsof -i :1337

# Terminar proceso
kill -9 <PID>

# O cambiar puerto en .env
PORT=1338
```

#### 2. Error de Permisos de Base de Datos

```bash
# Verificar permisos del directorio .tmp
ls -la .tmp/

# Corregir permisos si es necesario
chmod 755 .tmp/
chmod 644 .tmp/data.db
```

#### 3. Dependencias Desactualizadas

```bash
# Verificar dependencias obsoletas
npm outdated

# Actualizar dependencias
npm update

# Reinstalar node_modules
rm -rf node_modules package-lock.json
npm install
```

#### 4. Error en Migraciones

```bash
# Verificar logs de migración
DEBUG=strapi:database:migration npm run dev

# Validar datos de seed
node scripts/seed-manager.js validate

# Eliminar base de datos y reiniciar
rm .tmp/data.db
npm run dev
```

## Performance

### Optimizaciones de Desarrollo

1. **Cache de Node Modules**: Usar `npm ci` en CI/CD
2. **Hot Reload**: Configurado automáticamente en modo desarrollo
3. **Source Maps**: Habilitados para debugging

### Monitoreo

```bash
# Monitorear uso de memoria
node --inspect npm run dev

# Profiling de performance
node --prof npm run dev
```

## Testing

### Configuración de Tests

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: ['<rootDir>/tests/**/*.test.js'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js'
  ]
};
```

### Tipos de Tests

1. **Unit Tests**: Funciones individuales
2. **Integration Tests**: APIs y endpoints
3. **E2E Tests**: Flujos completos de usuario

## Seguridad

### Configuraciones de Seguridad

1. **Variables de Entorno**: Nunca commitear `.env`
2. **Secrets**: Usar gestores de secretos en producción
3. **CORS**: Configurar dominios permitidos
4. **Rate Limiting**: Implementar límites de requests

### Checklist de Seguridad

- [ ] Variables de entorno configuradas
- [ ] Secrets no expuestos en código
- [ ] CORS configurado correctamente
- [ ] Rate limiting implementado
- [ ] Validación de entrada habilitada
- [ ] Logs de seguridad configurados

## Recursos Adicionales

### Documentación

- [Strapi v5 Documentation](https://docs.strapi.io/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)
- [JavaScript Style Guide](https://github.com/airbnb/javascript)

### Comunidad

- [Strapi Discord](https://discord.strapi.io/)
- [Strapi Forum](https://forum.strapi.io/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/strapi)

### Herramientas

- [Postman Collection](./api/postman-collection.json)
- [VS Code Snippets](../.vscode/snippets.json)
- [Git Hooks](../.githooks/)

## Changelog

### v1.0.0 (2025-01-28)
- ✅ Configuración inicial del proyecto
- ✅ Documentación de setup completa
- ✅ Scripts de desarrollo configurados
- ✅ Guías de troubleshooting
- ✅ Configuración de herramientas de desarrollo