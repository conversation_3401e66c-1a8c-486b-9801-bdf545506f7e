'use strict';

const fs = require('fs');
const path = require('path');

// Función principal de migración (solo up() es soportado en Strapi v5)
async function up(knex) {
  console.log('🌱 Iniciando carga de datos iniciales...');
  
  try {
    // Leer el archivo JSON de datos iniciales
    const dataPath = path.join(__dirname, '../seeds/initial-data.json');
    const rawData = fs.readFileSync(dataPath, 'utf8');
    const seedData = JSON.parse(rawData);
    
    // Verificar que la estructura del JSON sea correcta
    if (!seedData.fields || !seedData.zones || !seedData.districts || !seedData.churches) {
      throw new Error('Estructura de datos inválida en initial-data.json');
    }
    
    console.log(`📊 Datos a procesar:`);
    console.log(`   - Fields: ${seedData.fields.length}`);
    console.log(`   - Zones: ${seedData.zones.length}`);
    console.log(`   - Districts: ${seedData.districts.length}`);
    console.log(`   - Churches: ${seedData.churches.length}`);
    
    // 1. Insertar Fields primero (no tienen dependencias)
    console.log('📝 Insertando Fields...');
    let fieldsInserted = 0;
    const fieldIds = {};
    
    for (const field of seedData.fields) {
      // Verificar si el field ya existe
      const existingFields = await knex('fields').where({ name: field.name }).select('*');
      
      if (existingFields.length === 0) {
        const [insertedField] = await knex('fields').insert({
          document_id: field.documentId,
          name: field.name,
          acronym: field.acronym,
          type: field.type,
          published_at: field.publishedAt,
          locale: field.locale,
          created_at: new Date(),
          updated_at: new Date()
        }).returning('*');
        
        fieldIds[field.name] = insertedField.id;
        fieldsInserted++;
        console.log(`   ✅ Field insertado: ${field.name}`);
      } else {
        fieldIds[field.name] = existingFields[0].id;
        console.log(`   ⚠️ Field ya existe: ${field.name}`);
      }
    }
    console.log(`   📈 Total Fields insertados: ${fieldsInserted}`);
    
    // 2. Insertar Zones (dependen de Fields)
    console.log('🗺️ Insertando Zones...');
    let zonesInserted = 0;
    const zoneIds = {};
    
    for (const zone of seedData.zones) {
       // Buscar el field relacionado primero
       const fieldId = fieldIds[zone.field_name];
       if (!fieldId) {
         console.warn(`   ⚠️ Field '${zone.field_name}' no encontrado para zone '${zone.name}'`);
         continue;
       }
       
       // Verificar si la zone ya existe dentro del mismo field usando JOIN con tabla de unión
       const existingZones = await knex('zones')
         .join('zones_field_lnk', 'zones.id', 'zones_field_lnk.zone_id')
         .where({ 
           'zones.name': zone.name,
           'zones_field_lnk.field_id': fieldId
         })
         .select('zones.*');
       
       if (existingZones.length === 0) {
         const [insertedZone] = await knex('zones').insert({
           document_id: zone.documentId,
           name: zone.name,
           published_at: zone.publishedAt,
           locale: zone.locale,
           created_at: new Date(),
           updated_at: new Date()
         }).returning('*');
         
         // Crear la relación en la tabla de unión
         await knex('zones_field_lnk').insert({
           zone_id: insertedZone.id,
           field_id: fieldId
         });
         
         // Usar clave compuesta para identificar zones únicas
         const zoneKey = `${zone.field_name}|${zone.name}`;
         zoneIds[zoneKey] = insertedZone.id;
         zonesInserted++;
         console.log(`   ✅ Zone insertada: ${zone.name} en field ${zone.field_name}`);
       } else {
         // Usar clave compuesta para zones existentes también
         const zoneKey = `${zone.field_name}|${zone.name}`;
         zoneIds[zoneKey] = existingZones[0].id;
         console.log(`   ⚠️ Zone ya existe: ${zone.name} en field ${zone.field_name}`);
       }
     }
    console.log(`   📈 Total Zones insertadas: ${zonesInserted}`);
    
    // 3. Insertar Districts (dependen de Zones)
    console.log('🏘️ Insertando Districts...');
    let districtsInserted = 0;
    const districtIds = {};
    
    for (const district of seedData.districts) {
       // Primero encontrar el field_name desde la zone correspondiente
       const zoneData = seedData.zones.find(z => z.name === district.zone_name);
       if (!zoneData) {
         console.warn(`   ⚠️ Zone '${district.zone_name}' no encontrada en datos para district '${district.name}'`);
         continue;
       }
       
       // Buscar la zone relacionada usando clave compuesta
       const zoneKey = `${zoneData.field_name}|${district.zone_name}`;
       const zoneId = zoneIds[zoneKey];
       if (!zoneId) {
         console.warn(`   ⚠️ Zone '${district.zone_name}' en field '${zoneData.field_name}' no encontrada para district '${district.name}'`);
         continue;
       }
       
       // Verificar si el district ya existe dentro de la misma zone usando JOIN con tabla de unión
       const existingDistricts = await knex('districts')
         .join('districts_zone_lnk', 'districts.id', 'districts_zone_lnk.district_id')
         .where({ 
           'districts.name': district.name,
           'districts_zone_lnk.zone_id': zoneId
         })
         .select('districts.*');
       
       if (existingDistricts.length === 0) {
         const [insertedDistrict] = await knex('districts').insert({
           document_id: district.documentId,
           name: district.name,
           published_at: district.publishedAt,
           locale: district.locale,
           created_at: new Date(),
           updated_at: new Date()
         }).returning('*');
         
         // Crear la relación en la tabla de unión
         await knex('districts_zone_lnk').insert({
           district_id: insertedDistrict.id,
           zone_id: zoneId
         });
         
         // Usar clave compuesta para identificar districts únicos
         const districtKey = `${zoneData.field_name}|${district.zone_name}|${district.name}`;
         districtIds[districtKey] = insertedDistrict.id;
         districtsInserted++;
         console.log(`   ✅ District insertado: ${district.name} en zone ${district.zone_name}`);
       } else {
         // Usar clave compuesta para districts existentes también
         const districtKey = `${zoneData.field_name}|${district.zone_name}|${district.name}`;
         districtIds[districtKey] = existingDistricts[0].id;
         console.log(`   ⚠️ District ya existe: ${district.name} en zone ${district.zone_name}`);
       }
     }
    console.log(`   📈 Total Districts insertados: ${districtsInserted}`);
    
    // 4. Insertar Churches (dependen de Districts)
    console.log('⛪ Insertando Churches...');
    let churchesInserted = 0;
    
    for (const church of seedData.churches) {
       // Primero encontrar el field_name desde la zone correspondiente
       const churchZoneData = seedData.zones.find(z => z.name === church.zone_name);
       if (!churchZoneData) {
         console.warn(`   ⚠️ Zone '${church.zone_name}' no encontrada en datos para church '${church.name}'`);
         continue;
       }
       
       // Buscar el district relacionado usando clave compuesta
       const districtKey = `${churchZoneData.field_name}|${church.zone_name}|${church.district_name}`;
       const districtId = districtIds[districtKey];
       if (!districtId) {
         console.warn(`   ⚠️ District '${church.district_name}' en zone '${church.zone_name}' no encontrado para church '${church.name}'`);
         continue;
       }
       
       // Verificar si la church ya existe dentro del mismo district usando JOIN con tabla de unión
       const existingChurches = await knex('churches')
         .join('churches_district_lnk', 'churches.id', 'churches_district_lnk.church_id')
         .where({ 
           'churches.name': church.name,
           'churches_district_lnk.district_id': districtId
         })
         .select('churches.*');
       
       if (existingChurches.length === 0) {
         const [insertedChurch] = await knex('churches').insert({
           document_id: church.documentId,
           name: church.name,
           published_at: church.publishedAt,
           locale: church.locale,
           created_at: new Date(),
           updated_at: new Date()
         }).returning('*');
         
         // Crear la relación en la tabla de unión
         await knex('churches_district_lnk').insert({
           church_id: insertedChurch.id,
           district_id: districtId
         });
         
         churchesInserted++;
         console.log(`   ✅ Church insertada: ${church.name} en district ${church.district_name}`);
       } else {
         console.log(`   ⚠️ Church ya existe: ${church.name} en district ${church.district_name}`);
       }
     }
    console.log(`   📈 Total Churches insertadas: ${churchesInserted}`);
    
    console.log('✅ Migración de datos iniciales completada exitosamente!');
    console.log(`📈 Resumen final:`);
    console.log(`   - Fields: ${fieldsInserted} insertados`);
    console.log(`   - Zones: ${zonesInserted} insertadas`);
    console.log(`   - Districts: ${districtsInserted} insertados`);
    console.log(`   - Churches: ${churchesInserted} insertadas`);
    
  } catch (error) {
    console.error('❌ Error durante la migración de datos iniciales:', error);
    throw error; // Re-lanzar el error para que Strapi maneje el rollback
  }
}

module.exports = { up };