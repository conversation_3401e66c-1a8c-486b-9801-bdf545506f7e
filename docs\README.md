# 📚 Documentación del Proyecto SCMP

## Índice

- [Introducción](#introducción)
- [Arquitectura del Sistema](#arquitectura-del-sistema)
- [Base de Datos](#base-de-datos)
- [APIs y Endpoints](#apis-y-endpoints)
- [Desarrollo](#desarrollo)
- [Despliegue](#despliegue)
- [Mantenimiento](#mantenimiento)

## Introducción

**SCMP Backend & Back Office** - Sistema de Competencia y Ministerio Pastoral construido con Strapi v5.

### ⚠️ Importante: Arquitectura del Sistema

Esta aplicación constituye **únicamente el backend y back office** de un sistema más amplio:

- **🔧 Backend API**: Proporciona todos los endpoints REST para la gestión de datos
- **🖥️ Back Office**: Panel administrativo de Strapi para mantenimiento del sistema
- **📊 Gestión de Datos Maestros**: Mantenimiento de iglesias, campos, zonas, distritos y configuraciones

### 🏗️ Arquitectura Completa del Sistema

```
┌─────────────────────┐    ┌─────────────────────┐
│   Frontend React    │    │  Backend & Back     │
│   (Aplicación       │◄──►│  Office (Esta App)  │
│   Separada)         │    │                     │
└─────────────────────┘    └─────────────────────┘
│                      │    │                     │
│ • Creación reuniones │    │ • API REST          │
│ • Gestión agenda     │    │ • Panel Admin       │
│ • Marcar asistencia  │    │ • Datos maestros    │
│ • Procesos operativos│    │ • Configuraciones   │
└─────────────────────┘    └─────────────────────┘
```

### 🎯 Responsabilidades de Esta Aplicación

**✅ Lo que SÍ maneja:**
- Gestión de la estructura organizacional (Fields → Zones → Districts → Churches)
- Mantenimiento de datos maestros
- Configuraciones del sistema
- API REST para consumo del frontend
- Panel administrativo para configuración

**❌ Lo que NO maneja (Frontend React):**
- Creación y gestión de reuniones
- Gestión de agenda
- Marcado de asistencia
- Procesos operativos diarios
- Interfaz de usuario final

Este sistema permite gestionar la estructura organizacional base de la iglesia, proporcionando los datos y APIs necesarios para que el frontend React implemente los procesos operativos.

## Enlaces Rápidos

- [🌱 Sistema de Seeds y Migraciones](./database/seeds-migrations.md)
- [🏗️ Arquitectura de la Base de Datos](./database/architecture.md)
- [🔧 Guía de Desarrollo](./development/setup.md)
- [🚀 Guía de Despliegue](./deployment/guide.md)
- [📋 API Reference](./api/reference.md)
- [🔒 Seguridad](./security/security.md)

## Estructura del Proyecto

```
scmp-cm/
├── docs/                    # Documentación del proyecto
├── src/                     # Código fuente
├── database/               # Migraciones y seeds
├── config/                 # Configuraciones
├── scripts/                # Scripts de utilidad
└── public/                 # Archivos públicos
```

## Tecnologías Principales

- **Backend**: Strapi v5
- **Base de Datos**: SQLite (desarrollo) / PostgreSQL (producción)
- **Node.js**: v18+
- **TypeScript**: Para tipado estático

## Primeros Pasos

1. Consulta la [Guía de Configuración](./development/setup.md)
2. Revisa la [Arquitectura de la Base de Datos](./database/architecture.md)
3. Familiarízate con el [Sistema de Seeds](./database/seeds-migrations.md)

## Contribución

Para contribuir al proyecto, consulta nuestra [Guía de Contribución](./development/contributing.md).