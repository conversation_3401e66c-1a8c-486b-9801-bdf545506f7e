/**
 * Servicio para el modelo EventType
 * Contiene la lógica de negocio para los tipos de eventos
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::event-type.event-type', ({ strapi }) => ({
  /**
   * Buscar tipos de eventos con filtros personalizados
   */
  async findWithFilters(params = {}) {
    return await strapi.entityService.findMany('api::event-type.event-type', {
      ...params,
      populate: {
        events: {
          fields: ['id', 'title', 'date', 'eventStatus']
        }
      }
    });
  },

  /**
   * Obtener un tipo de evento por nombre
   */
  async findByName(name: string) {
    return await strapi.entityService.findMany('api::event-type.event-type', {
      filters: {
        name: {
          $eq: name
        }
      },
      limit: 1
    });
  },

  /**
   * Obtener estadísticas de eventos por tipo
   */
  async getEventStatsByType(eventTypeId: number) {
    const eventType = await strapi.entityService.findOne('api::event-type.event-type', eventTypeId, {
      populate: {
        events: {
          fields: ['id', 'eventStatus', 'date']
        }
      }
    });

    if (!eventType) {
      return null;
    }

    const events = (eventType as any).events || [];
    const stats = {
      total: events.length,
      programada: events.filter(e => e.eventStatus === 'programada').length,
      'en-progreso': events.filter(e => e.eventStatus === 'en-progreso').length,
      completada: events.filter(e => e.eventStatus === 'completada').length,
      cancelada: events.filter(e => e.eventStatus === 'cancelada').length
    };

    return {
      eventType: {
        id: eventType.id,
        name: eventType.name,
        autoRegistrationDefault: eventType.autoRegistrationDefault,
        attendanceMethodDefault: eventType.attendanceMethodDefault
      },
      stats
    };
  }
}));