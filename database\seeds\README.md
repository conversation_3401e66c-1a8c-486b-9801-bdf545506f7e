# 🌱 Seeds - Datos Iniciales para SCMP

## Descripción

Este directorio contiene los archivos de semilla (seeds) para cargar datos iniciales en la base de datos del sistema SCMP (Sistema de Control y Manejo Pastoral).

## Estructura de Archivos

### `initial-data.json`
Archivo JSON que contiene los datos iniciales estructurados para:
- **Fields** (Campos): Divisiones territoriales principales
- **Zones** (Zonas): Subdivisiones dentro de cada campo
- **Districts** (Distritos): Subdivisiones dentro de cada zona
- **Churches** (Iglesias): Iglesias ubicadas en cada distrito

### Estructura del JSON

```json
{
  "fields": [
    {
      "name": "Nombre del Campo",
      "acronym": "Acrónimo (opcional)",
      "type": "Tipo de campo (opcional)"
    }
  ],
  "zones": [
    {
      "name": "Nombre de la Zona",
      "field_name": "Nombre del Campo al que pertenece"
    }
  ],
  "districts": [
    {
      "name": "Nombre del Distrito",
      "zone_name": "Nombre de la Zona a la que pertenece"
    }
  ],
  "churches": [
    {
      "name": "Nombre de la Iglesia",
      "district_name": "Nombre del Distrito al que pertenece"
    }
  ]
}
```

## Jerarquía de Relaciones

```
Field (Campo)
└── Zone (Zona)
    └── District (Distrito)
        └── Church (Iglesia)
```

## Cómo Personalizar los Datos

### 1. Editar el archivo JSON
Modifica el archivo `initial-data.json` con tus propios datos:

```json
{
  "fields": [
    {
      "name": "Tu Campo Personalizado",
      "acronym": "TCP",
      "type": "Urbano"
    }
  ],
  "zones": [
    {
      "name": "Tu Zona Personalizada",
      "field_name": "Tu Campo Personalizado"
    }
  ]
  // ... continúa con districts y churches
}
```

### 2. Consideraciones Importantes

- **Nombres únicos**: Asegúrate de que los nombres sean únicos dentro de cada tipo
- **Relaciones correctas**: Verifica que los `field_name`, `zone_name` y `district_name` coincidan exactamente con los nombres definidos
- **Orden jerárquico**: Respeta la jerarquía Field → Zone → District → Church

### 3. Campos Opcionales

- **Field.acronym**: Acrónimo del campo (ej: "CN" para "Campo Norte")
- **Field.type**: Tipo de campo (ej: "Urbano", "Rural", "Mixto")

## Ejecución de la Migración

### Aplicar la migración (cargar datos)
```bash
npm run strapi db:migrate
```

### Revertir la migración (eliminar datos)
```bash
npm run strapi db:migrate:down
```

## Validaciones

La migración incluye las siguientes validaciones:

- ✅ **Duplicados**: No inserta registros si ya existen
- ✅ **Relaciones**: Verifica que las relaciones padre existan antes de insertar
- ✅ **Logs detallados**: Muestra el progreso y errores durante la ejecución
- ✅ **Rollback seguro**: Permite revertir todos los cambios

## Ejemplo de Salida de la Migración

```
🌱 Iniciando carga de datos iniciales...
📍 Insertando Fields...
  ✅ Field creado: Campo Norte
  ✅ Field creado: Campo Sur
🗺️  Insertando Zones...
  ✅ Zone creada: Zona Central Norte
  ✅ Zone creada: Zona Periférica Norte
🏘️  Insertando Districts...
  ✅ District creado: Distrito Centro
⛪ Insertando Churches...
  ✅ Church creada: Iglesia Central
🎉 Datos iniciales cargados exitosamente!
```

## Troubleshooting

### Error: "Field no encontrado"
- Verifica que el `field_name` en zones coincida exactamente con el `name` en fields

### Error: "Zone no encontrada"
- Verifica que el `zone_name` en districts coincida exactamente con el `name` en zones

### Error: "District no encontrado"
- Verifica que el `district_name` en churches coincida exactamente con el `name` en districts

### Error: "Duplicate entry"
- La migración maneja duplicados automáticamente, pero si ves este error, verifica que no haya nombres duplicados en el JSON

## Notas Adicionales

- Los timestamps (`created_at`, `updated_at`) se establecen automáticamente
- Los campos de auditoría (`created_by`, `updated_by`) se pueden establecer manualmente si es necesario
- La migración es idempotente: se puede ejecutar múltiples veces sin problemas