# 🌱 Guía Completa del Enfoque Manual para Migraciones - SCMP

## 📋 Resumen del Enfoque Manual

Este proyecto utiliza un **enfoque manual** para la gestión de datos y migraciones, conectándose directamente a la base de datos de Supabase. Este enfoque garantiza:

- ✅ **Control total** sobre las operaciones de base de datos
- ✅ **Validación de duplicados** antes de insertar
- ✅ **Logs detallados** de cada operación
- ✅ **Manejo de errores** robusto
- ✅ **Conexión directa** a Supabase sin dependencias de Strapi

## 🗂️ Estructura de Scripts Manuales

### 📄 Scripts Principales

1. **`manual-seed.js`** - Script principal para sembrar datos iniciales (Fields, Zones, Districts, Churches)
2. **`manual-seed-event-types.js`** - Script específico para sembrar tipos de eventos
3. **`manual-seed-departments.js`** - Script específico para sembrar departamentos
4. **`validate-data.js`** - Script para validar que todos los datos estén completos
5. **`check-duplicates.js`** - Script para detectar y eliminar duplicados

### 📊 Datos de Origen

- **`database/seeds/initial-data.json`** - Archivo JSON con los datos estructurados
- **`database/data/departments-data.json`** - Archivo JSON con los departamentos
- **`database/migrations/2025-01-28-000001-seed-event-types.js`** - Migración con tipos de eventos (solo como referencia)
- **`database/migrations/2025-01-28-000002-seed-departments.js`** - Migración con departamentos (solo como referencia)

## 🚀 Uso Rápido del Enfoque Manual

### 1. Sembrar datos iniciales (Fields, Zones, Districts, Churches)
```bash
node manual-seed.js
```

### 2. Sembrar tipos de eventos
```bash
node manual-seed-event-types.js
```

### 3. Sembrar departamentos
```bash
node manual-seed-departments.js
```

### 4. Validar que todos los datos estén completos
```bash
node validate-data.js
```

### 5. Verificar y eliminar duplicados
```bash
node check-duplicates.js
```

## 📊 Estructura de Datos

### Jerarquía
```
Field (Campo)
├── Zone (Zona)
│   ├── District (Distrito)
│   │   ├── Church (Iglesia)
│   │   └── Church (Iglesia)
│   └── District (Distrito)
└── Zone (Zona)
```

### Ejemplo de JSON
```json
{
  "fields": [
    {
      "name": "Campo Norte",
      "acronym": "CN",
      "type": "Urbano"
    }
  ],
  "zones": [
    {
      "name": "Zona Central Norte",
      "field_name": "Campo Norte"
    }
  ],
  "districts": [
    {
      "name": "Distrito Centro",
      "zone_name": "Zona Central Norte"
    }
  ],
  "churches": [
    {
      "name": "Iglesia Central",
      "district_name": "Distrito Centro"
    }
  ]
}
```

## 🔧 Personalización

### 1. Editar Datos
Modifica el archivo `database/seeds/initial-data.json` con tus propios datos:

```json
{
  "fields": [
    {
      "name": "Tu Campo Personalizado",
      "acronym": "TCP",
      "type": "Rural"
    }
  ],
  "zones": [
    {
      "name": "Tu Zona Personalizada",
      "field_name": "Tu Campo Personalizado"
    }
  ]
  // ... continúa con districts y churches
}
```

### 2. Validar Cambios
```bash
npm run seeds:validate
```

### 3. Cargar Nuevos Datos
```bash
npm run seeds:load
```

## ⚙️ Configuración de Base de Datos

### 🔧 Configuración de Conexión

Todos los scripts manuales utilizan la misma configuración de conexión a Supabase:

```javascript
const dbConfig = {
  host: 'aws-0-us-east-2.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.ktykxlboodjvnlabbwoy',
  password: 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};
```

### 📋 Scripts Disponibles

| Script | Comando | Descripción |
|--------|---------|-------------|
| **manual-seed.js** | `node manual-seed.js` | Siembra datos iniciales (Fields, Zones, Districts, Churches) |
| **manual-seed-event-types.js** | `node manual-seed-event-types.js` | Siembra tipos de eventos |
| **manual-seed-departments.js** | `node manual-seed-departments.js` | Siembra departamentos que pueden invitar a eventos |
| **validate-data.js** | `node validate-data.js` | Valida que todos los datos estén completos |
| **check-duplicates.js** | `node check-duplicates.js` | Detecta y eliminar duplicados |

## 🛡️ Características del Enfoque Manual

### ✅ Validaciones Robustas
- **Duplicados**: Verificación exhaustiva antes de insertar cualquier registro
- **Relaciones**: Validación de integridad referencial usando claves compuestas
- **Conexión**: Manejo robusto de errores de conexión a Supabase
- **Transacciones**: Operaciones atómicas para mantener consistencia
- **Logs detallados**: Seguimiento completo de cada operación

### 📊 Logs Detallados del Enfoque Manual

#### Ejemplo de `manual-seed.js`:
```
🔗 Conectado a la base de datos
📊 Datos cargados:
   - Fields: 4
   - Zones: 27
   - Districts: 181
   - Churches: 1075

📝 Insertando Fields...
   ✅ Field insertado: Campo Norte
   ⚠️ Field ya existe: Campo Sur
   📈 Total Fields insertados: 2

🗺️ Insertando Zones...
   ✅ Zone insertada: Zona Central Norte en field Campo Norte
   📈 Total Zones insertadas: 15

🏘️ Insertando Districts...
   ✅ District insertado: Distrito Centro en zone Zona Central Norte
   📈 Total Districts insertados: 95

⛪ Insertando Churches...
   📊 Total churches a procesar: 1075
   📈 Progreso: 100/1075 churches procesadas
   ✅ 50 churches insertadas hasta ahora...
   📈 Resumen Churches:
     - Insertadas: 500
     - Ya existían: 575
     - Errores: 0

✅ Inserción manual de datos completada exitosamente!
```

#### Ejemplo de `validate-data.js`:
```
📊 TOTALES ESPERADOS:
   - Fields: 4
   - Zones: 27
   - Districts: 181
   - Churches: 1075
   - Event Types: 22

📊 TOTALES REALES (en base de datos):
   - Fields: 4
   - Zones: 27
   - Districts: 181
   - Churches: 1075
   - Event Types: 22

✅ VALIDACIÓN:
   - Fields: ✅ (4/4)
   - Zones: ✅ (27/27)
   - Districts: ✅ (181/181)
   - Churches: ✅ (1075/1075)
   - Event Types: ✅ (22/22)

📈 RESULTADO FINAL:
✅ ¡TODOS LOS DATOS ESTÁN COMPLETOS!
```

## 🔍 Troubleshooting del Enfoque Manual

### Error: "Tenant or user not found" (PostgreSQL XX000)
**Causa**: Problema de autenticación con Supabase
**Solución**: 
- Verifica que las credenciales en `dbConfig` sean correctas
- Asegúrate de usar el host y puerto correctos: `aws-0-us-east-2.pooler.supabase.com:5432`
- Confirma que `ssl: { rejectUnauthorized: false }` esté configurado

### Error: "Field no encontrado para zone"
**Causa**: El `field_name` en zones no coincide con ningún `name` en fields
**Solución**: 
- Verifica que los nombres coincidan exactamente (case-sensitive)
- Ejecuta `node validate-data.js` para verificar el estado actual

### Error: "Zone no encontrada para district"
**Causa**: El `zone_name` en districts no coincide con ningún `name` en zones
**Solución**: 
- Verifica que los nombres coincidan exactamente
- Revisa que la zone esté en el field correcto usando claves compuestas

### Error: "District no encontrado para church"
**Causa**: El `district_name` en churches no coincide con ningún `name` en districts
**Solución**: 
- Verifica que los nombres coincidan exactamente
- Confirma que el district esté en la zone correcta

### Error de conexión a base de datos
**Causa**: Problemas de red o configuración de Supabase
**Solución**: 
- Verifica tu conexión a internet
- Confirma que Supabase esté funcionando
- Revisa que las credenciales no hayan expirado

### Datos duplicados detectados
**Causa**: Ejecución múltiple de scripts o datos inconsistentes
**Solución**: 
- Ejecuta `node check-duplicates.js` para limpiar duplicados
- Usa `node validate-data.js` para verificar el estado final

## 📝 Notas Importantes del Enfoque Manual

1. **Idempotencia**: Los scripts se pueden ejecutar múltiples veces sin problemas - detectan duplicados automáticamente
2. **Orden de inserción**: Se respeta estrictamente la jerarquía Field → Zone → District → Church → Event Types
3. **Claves compuestas**: Se usan claves compuestas para permitir nombres duplicados en diferentes contextos
4. **Conexión directa**: No depende de Strapi - se conecta directamente a Supabase
5. **Manejo de errores**: Cada script tiene manejo robusto de errores y logs detallados
6. **Validación continua**: Siempre verifica la existencia antes de insertar

## 🎯 Flujo de Trabajo Recomendado

### Para Nuevas Migraciones:

1. **Preparar datos**: Actualiza `database/seeds/initial-data.json` con nuevos datos
2. **Validar estado actual**: `node validate-data.js`
3. **Sembrar datos principales**: `node manual-seed.js`
4. **Sembrar tipos de eventos**: `node manual-seed-event-types.js`
5. **Verificar duplicados**: `node check-duplicates.js`
6. **Validación final**: `node validate-data.js`

### Para Mantenimiento:

1. **Verificar estado**: `node validate-data.js`
2. **Limpiar duplicados**: `node check-duplicates.js`
3. **Re-validar**: `node validate-data.js`

## 🔄 Creando Nuevos Scripts Manuales

### Plantilla Base para Nuevos Scripts:

```javascript
const { Client } = require('pg');

// Configuración estándar de Supabase
const dbConfig = {
  host: 'aws-0-us-east-2.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.ktykxlboodjvnlabbwoy',
  password: 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};

async function nuevoScript() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');
    
    // Tu lógica aquí
    
    console.log('✅ Script completado exitosamente!');
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.end();
    console.log('🔌 Conexión cerrada');
  }
}

nuevoScript();
```

## 📞 Soporte y Troubleshooting

Si encuentras algún problema:

1. **Revisa los logs detallados** en la consola - cada script proporciona información específica
2. **Ejecuta `node validate-data.js`** para verificar el estado actual de los datos
3. **Usa `node check-duplicates.js`** si sospechas problemas de duplicados
4. **Verifica la conexión** a Supabase con las credenciales correctas
5. **Consulta la sección de Troubleshooting** arriba para errores específicos

---

**¡Perfecto!** Ya tienes un sistema manual robusto y bien documentado para gestionar migraciones de datos en tu proyecto SCMP. 🎉

### ✅ Ventajas del Enfoque Manual:
- Control total sobre las operaciones
- Logs detallados y transparentes
- Manejo robusto de errores
- Validación exhaustiva de duplicados
- Independiente de frameworks
- Fácil de mantener y extender