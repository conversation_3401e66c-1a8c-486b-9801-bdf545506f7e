/**
 * This file was automatically generated by Strap<PERSON>.
 * Any modifications made will be discarded.
 */
import strapiCloud from "@strapi/plugin-cloud/strapi-admin";
import documentation from "@strapi/plugin-documentation/strapi-admin";
import usersPermissions from "@strapi/plugin-users-permissions/strapi-admin";
import customPermissions from "../../src/plugins/custom-permissions/./dist/admin/index.mjs";
import { renderAdmin } from "@strapi/strapi/admin";

import customisations from "../../src/admin/app.tsx";

renderAdmin(document.getElementById("strapi"), {
  customisations,

  plugins: {
    "strapi-cloud": strapiCloud,
    documentation: documentation,
    "users-permissions": usersPermissions,
    "custom-permissions": customPermissions,
  },
});
