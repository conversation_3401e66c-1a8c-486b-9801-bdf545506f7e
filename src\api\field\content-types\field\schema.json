{"kind": "collectionType", "collectionName": "fields", "info": {"singularName": "field", "pluralName": "fields", "displayName": "Field"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "acronym": {"type": "string"}, "type": {"type": "string"}}}