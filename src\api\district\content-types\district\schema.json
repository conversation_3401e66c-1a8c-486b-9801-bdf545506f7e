{"kind": "collectionType", "collectionName": "districts", "info": {"singularName": "district", "pluralName": "districts", "displayName": "District"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"name": {"type": "string", "required": true}, "zone": {"type": "relation", "relation": "manyToOne", "target": "api::zone.zone"}}}