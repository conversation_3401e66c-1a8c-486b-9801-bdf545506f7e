{"collectionName": "components_session_attendance_sessions", "info": {"displayName": "attendance-session", "description": "Session attendance details"}, "options": {}, "attributes": {"sessionId": {"type": "integer", "required": true, "unique": true}, "date": {"type": "date", "required": true}, "comment": {"type": "text"}, "attendanceMode": {"type": "enumeration", "enum": ["normal", "kiosk"], "required": true, "default": "normal"}, "isDefault": {"type": "boolean", "required": true, "default": false}, "status": {"type": "enumeration", "enum": ["pendiente", "en_progreso", "completada", "cancelada"], "required": true, "default": "pendiente"}}}