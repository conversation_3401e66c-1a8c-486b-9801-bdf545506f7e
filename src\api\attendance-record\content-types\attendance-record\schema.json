{"kind": "collectionType", "collectionName": "attendance_records", "info": {"singularName": "attendance-record", "pluralName": "attendance-records", "displayName": "AttendanceRecord"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"sessionId": {"type": "integer", "required": true}, "attended": {"type": "boolean", "required": true}, "notes": {"type": "text"}, "partialAttendance": {"type": "boolean"}, "partialAttendanceComment": {"type": "text"}, "timeStayed": {"type": "text"}, "participant": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "event": {"type": "relation", "relation": "manyToOne", "target": "api::event.event", "inversedBy": "attendanceRecords"}}}