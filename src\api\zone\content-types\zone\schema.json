{"kind": "collectionType", "collectionName": "zones", "info": {"singularName": "zone", "pluralName": "zones", "displayName": "Zone"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"name": {"type": "string", "required": true}, "field": {"type": "relation", "relation": "manyToOne", "target": "api::field.field"}}}