/**
 * Script de migración manual para poblar departamentos
 * Enfoque manual con conexión directa a Supabase
 * 
 * Uso: node manual-seed-departments.js
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Cargar variables de entorno
require('dotenv').config();

// Configuración de la base de datos PostgreSQL
const client = new Client({
  host: process.env.DATABASE_HOST || 'localhost',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || 'strapi',
  user: process.env.DATABASE_USERNAME || 'strapi',
  password: process.env.DATABASE_PASSWORD || 'strapi',
  ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false
});

/**
 * Función para cargar datos desde archivo JSON
 */
function loadDepartmentsData() {
  try {
    const dataPath = path.join(__dirname, 'database', 'data', 'departments-data.json');
    const rawData = fs.readFileSync(dataPath, 'utf8');
    const data = JSON.parse(rawData);
    
    console.log(`✅ Datos cargados: ${data.departments.length} departamentos`);
    return data.departments;
  } catch (error) {
    console.error('❌ Error al cargar datos de departamentos:', error.message);
    throw error;
  }
}

/**
 * Función para verificar si un departamento ya existe
 */
async function departmentExists(name) {
  try {
    const query = 'SELECT id, name FROM departments WHERE name = $1';
    const result = await client.query(query, [name]);
    
    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error(`❌ Error al verificar departamento ${name}:`, error.message);
    return null;
  }
}

/**
 * Función para insertar un departamento
 */
async function insertDepartment(department) {
  try {
    const query = `
      INSERT INTO departments (name, description, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, name, description, is_active
    `;
    
    const values = [
      department.name,
      department.description,
      department.isActive,
      new Date().toISOString(),
      new Date().toISOString()
    ];
    
    const result = await client.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error(`❌ Error al insertar departamento ${department.name}:`, error.message);
    throw error;
  }
}

/**
 * Función principal para poblar departamentos
 */
async function seedDepartments() {
  console.log('🚀 Iniciando migración de departamentos...');
  console.log('=' .repeat(50));
  
  try {
    // Conectar a la base de datos
    await client.connect();
    console.log('✅ Conexión a la base de datos establecida');
    
    // Verificar que la tabla departments existe
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'departments'
      )
    `);
    
    if (!tableCheck.rows[0].exists) {
      console.error('❌ La tabla departments no existe. Ejecuta las migraciones primero.');
      return;
    }
    
    // Cargar datos de departamentos
    const departments = loadDepartmentsData();
    
    let insertedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    console.log(`\n📋 Procesando ${departments.length} departamentos...`);
    console.log('-'.repeat(50));
    
    // Procesar cada departamento
    for (const department of departments) {
      try {
        console.log(`\n🔍 Procesando: ${department.name}`);
        
        // Verificar si el departamento ya existe
        const existing = await departmentExists(department.name);
        
        if (existing) {
          console.log(`⚠️  Departamento ya existe: ${department.name} (ID: ${existing.id})`);
          skippedCount++;
          continue;
        }
        
        // Insertar nuevo departamento
        const inserted = await insertDepartment(department);
        console.log(`✅ Departamento insertado: ${inserted.name} (ID: ${inserted.id})`);
        insertedCount++;
        
      } catch (error) {
        console.error(`❌ Error procesando ${department.name}:`, error.message);
        errorCount++;
      }
    }
    
    // Resumen final
    console.log('\n' + '='.repeat(50));
    console.log('📊 RESUMEN DE MIGRACIÓN');
    console.log('='.repeat(50));
    console.log(`✅ Departamentos insertados: ${insertedCount}`);
    console.log(`⚠️  Departamentos omitidos: ${skippedCount}`);
    console.log(`❌ Errores: ${errorCount}`);
    console.log(`📋 Total procesados: ${departments.length}`);
    
    if (errorCount === 0) {
      console.log('\n🎉 Migración completada exitosamente!');
    } else {
      console.log('\n⚠️  Migración completada con errores. Revisar logs arriba.');
    }
    
  } catch (error) {
    console.error('❌ Error fatal en la migración:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cerrar conexión a la base de datos
    try {
      await client.end();
      console.log('🔌 Conexión a la base de datos cerrada');
    } catch (error) {
      console.error('❌ Error al cerrar conexión:', error.message);
    }
  }
}

/**
 * Función para mostrar ayuda
 */
function showHelp() {
  console.log('\n📖 AYUDA - Script de Migración de Departamentos');
  console.log('='.repeat(50));
  console.log('Uso: node manual-seed-departments.js [opción]');
  console.log('\nOpciones:');
  console.log('  --help, -h    Mostrar esta ayuda');
  console.log('  --dry-run     Simular la migración sin insertar datos');
  console.log('\nEjemplos:');
  console.log('  node manual-seed-departments.js');
  console.log('  node manual-seed-departments.js --dry-run');
  console.log('\nArchivos requeridos:');
  console.log('  - database/data/departments-data.json');
  console.log('\nVariables de entorno:');
  console.log('  - DATABASE_URL (URL de conexión a PostgreSQL)');
  console.log('  - DATABASE_PASSWORD (Clave de Supabase)');
}

/**
 * Función para dry run (simulación)
 */
async function dryRun() {
  console.log('🧪 MODO DRY RUN - Simulación de migración');
  console.log('='.repeat(50));
  
  try {
    const departments = loadDepartmentsData();
    
    console.log(`\n📋 Se procesarían ${departments.length} departamentos:`);
    console.log('-'.repeat(50));
    
    departments.forEach((department, index) => {
      console.log(`${index + 1}. ${department.name}`);
      console.log(`   Descripción: ${department.description}`);
      console.log(`   Activo: ${department.isActive}`);
      console.log('');
    });
    
    console.log('✅ Simulación completada. Usa el comando sin --dry-run para ejecutar la migración real.');
    
  } catch (error) {
    console.error('❌ Error en dry run:', error.message);
  }
}

// Ejecutar script
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
  } else if (args.includes('--dry-run')) {
    dryRun();
  } else {
    seedDepartments();
  }
}

module.exports = {
  seedDepartments,
  loadDepartmentsData,
  departmentExists,
  insertDepartment
};