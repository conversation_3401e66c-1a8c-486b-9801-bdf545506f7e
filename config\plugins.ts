module.exports = ({ env }) => ({
    documentation: {
      enabled: true,
      config: {
        openapi: '3.0.0',
        info: {
          version: '1.0.0',
          title: 'DOCUMENTATION',
          description: '',
          termsOfService: 'YOUR_TERMS_OF_SERVICE_URL',
          contact: {
            name: 'TEAM',
            email: '<EMAIL>',
            url: 'mywebsite.io'
          },
          license: {
            name: 'Apache 2.0',
            url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
          },
        },
        'x-strapi-config': {
          // Leave empty to ignore plugins during generation
          plugins: [ 'upload', 'users-permissions'],
          path: '/documentation',
        },
        servers: [{ url: 'http://localhost:1337/api', description: 'Development server' }],
        externalDocs: {
          description: 'Find out more',
          url: 'https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html'
        },
        security: [ { bearerAuth: [] } ]
      }
    },
    'custom-permissions': {
    enabled: true,
    resolve: './src/plugins/custom-permissions'
  },
  email: {
    config: {
      provider: '@strapi/provider-email-nodemailer',
      providerOptions: {
        host: env('SMTP_HOST', 'smtp.gmail.com'),
        port: env('SMTP_PORT', 587),
        secure: env.bool('SMTP_SECURE', false),
        auth: {
          user: env('SMTP_USERNAME'),
          pass: env('SMTP_PASSWORD'),
        },
        // Configuraciones adicionales de nodemailer (opcional)
        // tls: {
        //   rejectUnauthorized: env.bool('SMTP_REJECT_UNAUTHORIZED', true),
        // },
      },
      settings: {
        defaultFrom: env('EMAIL_FROM', '<EMAIL>'),
        defaultReplyTo: env('EMAIL_REPLY_TO', '<EMAIL>'),
      },
    },
  },
  });