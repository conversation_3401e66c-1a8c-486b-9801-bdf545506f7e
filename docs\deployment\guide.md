# 🚀 Guía de Despliegue - SCMP

## Introducción

Esta guía describe los procesos y mejores prácticas para desplegar el Sistema de Competencia y Ministerio Pastoral (SCMP) en diferentes entornos.

## Entornos de Despliegue

### 1. Desarrollo (Development)
- **Propósito**: Desarrollo local y testing
- **Base de Datos**: SQLite
- **Configuración**: Mínima
- **URL**: `http://localhost:1337`

### 2. Staging (Pruebas)
- **Propósito**: Testing pre-producción
- **Base de Datos**: PostgreSQL
- **Configuración**: Similar a producción
- **URL**: `https://staging.scmp.church`

### 3. Producción (Production)
- **Propósito**: Entorno live
- **Base de Datos**: PostgreSQL
- **Configuración**: Optimizada para performance
- **URL**: `https://scmp.church`

## Requisitos del Sistema

### Mínimos
- **CPU**: 1 vCPU
- **RAM**: 1 GB
- **Almacenamiento**: 10 GB SSD
- **Node.js**: v18.0.0+
- **PostgreSQL**: v13+

### Recomendados (Producción)
- **CPU**: 2+ vCPUs
- **RAM**: 4+ GB
- **Almacenamiento**: 50+ GB SSD
- **Node.js**: v20.0.0+
- **PostgreSQL**: v15+
- **Redis**: Para caché (opcional)

## Preparación para Despliegue

### 1. Configuración de Variables de Entorno

#### Archivo .env.production

```env
# Configuración de la aplicación
NODE_ENV=production
HOST=0.0.0.0
PORT=1337

# Claves de seguridad (generar nuevas para producción)
APP_KEYS=key1,key2,key3,key4
API_TOKEN_SALT=random-salt-here
ADMIN_JWT_SECRET=admin-jwt-secret-here
TRANSFER_TOKEN_SALT=transfer-token-salt-here
JWT_SECRET=jwt-secret-here

# Base de datos PostgreSQL
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=scmp_production
DATABASE_USERNAME=scmp_user
DATABASE_PASSWORD=secure-password-here
DATABASE_SSL=true

# Configuración de archivos
CLOUDINARY_NAME=your-cloudinary-name
CLOUDINARY_KEY=your-cloudinary-key
CLOUDINARY_SECRET=your-cloudinary-secret

# Email (opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Configuración adicional
ADMIN_PATH=/admin
PUBLIC_URL=https://scmp.church
```

### 2. Generación de Claves de Seguridad

```bash
# Generar claves aleatorias
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# O usar el generador de Strapi
npx strapi generate
```

### 3. Optimización del Código

```bash
# Instalar dependencias de producción
npm ci --only=production

# Construir la aplicación
npm run build

# Verificar que no hay vulnerabilidades
npm audit
```

## Despliegue en VPS/Servidor Dedicado

### 1. Configuración del Servidor

#### Actualizar Sistema

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# Instalar dependencias
sudo apt install -y curl wget git build-essential
```

#### Instalar Node.js

```bash
# Usando NodeSource
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verificar instalación
node --version
npm --version
```

#### Instalar PostgreSQL

```bash
# Instalar PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Iniciar servicio
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Crear base de datos y usuario
sudo -u postgres psql
```

```sql
-- En el prompt de PostgreSQL
CREATE DATABASE scmp_production;
CREATE USER scmp_user WITH ENCRYPTED PASSWORD 'secure-password-here';
GRANT ALL PRIVILEGES ON DATABASE scmp_production TO scmp_user;
\q
```

### 2. Configuración de la Aplicación

#### Clonar Repositorio

```bash
# Crear directorio para la aplicación
sudo mkdir -p /var/www/scmp
sudo chown $USER:$USER /var/www/scmp

# Clonar repositorio
cd /var/www/scmp
git clone https://github.com/your-org/scmp-cm.git .

# Instalar dependencias
npm ci --only=production
```

#### Configurar Variables de Entorno

```bash
# Copiar archivo de configuración
cp .env.example .env.production

# Editar configuración
nano .env.production
```

#### Construir Aplicación

```bash
# Construir para producción
NODE_ENV=production npm run build

# Verificar que la construcción fue exitosa
ls -la dist/
```

### 3. Configuración de PM2

#### Instalar PM2

```bash
# Instalar PM2 globalmente
npm install -g pm2

# Verificar instalación
pm2 --version
```

#### Archivo de Configuración PM2

```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'scmp-api',
      script: './dist/src/index.js',
      cwd: '/var/www/scmp',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 1337
      },
      env_file: '.env.production',
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    }
  ]
};
```

#### Iniciar Aplicación

```bash
# Crear directorio de logs
mkdir -p logs

# Iniciar aplicación
pm2 start ecosystem.config.js

# Verificar estado
pm2 status

# Ver logs
pm2 logs scmp-api

# Configurar inicio automático
pm2 startup
pm2 save
```

### 4. Configuración de Nginx

#### Instalar Nginx

```bash
# Instalar Nginx
sudo apt install -y nginx

# Iniciar y habilitar
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### Configuración del Sitio

```nginx
# /etc/nginx/sites-available/scmp
server {
    listen 80;
    server_name scmp.church www.scmp.church;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name scmp.church www.scmp.church;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/scmp.church/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/scmp.church/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # Main proxy configuration
    location / {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:1337;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Admin panel specific configuration
    location /admin {
        limit_req zone=api burst=5 nodelay;
        
        proxy_pass http://127.0.0.1:1337;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### Habilitar Sitio

```bash
# Crear enlace simbólico
sudo ln -s /etc/nginx/sites-available/scmp /etc/nginx/sites-enabled/

# Verificar configuración
sudo nginx -t

# Recargar Nginx
sudo systemctl reload nginx
```

### 5. Configuración de SSL con Let's Encrypt

```bash
# Instalar Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtener certificado
sudo certbot --nginx -d scmp.church -d www.scmp.church

# Configurar renovación automática
sudo crontab -e
# Agregar línea:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## Despliegue en Docker

### 1. Dockerfile

```dockerfile
# Dockerfile
FROM node:20-alpine AS builder

# Instalar dependencias del sistema
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Copiar archivos de dependencias
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copiar código fuente
COPY . .

# Construir aplicación
RUN npm run build

# Imagen de producción
FROM node:20-alpine AS runner

RUN apk add --no-cache libc6-compat

WORKDIR /app

# Crear usuario no-root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 strapi

# Copiar archivos construidos
COPY --from=builder --chown=strapi:nodejs /app/dist ./dist
COPY --from=builder --chown=strapi:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=strapi:nodejs /app/package.json ./package.json

# Crear directorios necesarios
RUN mkdir -p public/uploads && chown -R strapi:nodejs public

USER strapi

EXPOSE 1337

ENV NODE_ENV=production

CMD ["npm", "start"]
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    container_name: scmp-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_CLIENT=postgres
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_NAME=scmp
      - DATABASE_USERNAME=strapi
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
    ports:
      - "1337:1337"
    volumes:
      - ./public/uploads:/app/public/uploads
    depends_on:
      - db
    networks:
      - scmp-network

  db:
    image: postgres:15-alpine
    container_name: scmp-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=scmp
      - POSTGRES_USER=strapi
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
    volumes:
      - db-data:/var/lib/postgresql/data
    networks:
      - scmp-network

  nginx:
    image: nginx:alpine
    container_name: scmp-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - app
    networks:
      - scmp-network

volumes:
  db-data:

networks:
  scmp-network:
    driver: bridge
```

### 3. Comandos de Docker

```bash
# Construir y ejecutar
docker-compose up -d --build

# Ver logs
docker-compose logs -f app

# Ejecutar comandos en el contenedor
docker-compose exec app npm run strapi console

# Backup de base de datos
docker-compose exec db pg_dump -U strapi scmp > backup.sql

# Restaurar base de datos
docker-compose exec -T db psql -U strapi scmp < backup.sql
```

## Despliegue en Servicios Cloud

### 1. Heroku

#### Preparación

```bash
# Instalar Heroku CLI
npm install -g heroku

# Login
heroku login

# Crear aplicación
heroku create scmp-api
```

#### Configuración

```bash
# Configurar variables de entorno
heroku config:set NODE_ENV=production
heroku config:set NPM_CONFIG_PRODUCTION=false
heroku config:set APP_KEYS="key1,key2,key3,key4"
heroku config:set API_TOKEN_SALT="your-salt"
heroku config:set ADMIN_JWT_SECRET="your-secret"
heroku config:set JWT_SECRET="your-jwt-secret"

# Agregar PostgreSQL
heroku addons:create heroku-postgresql:mini

# Configurar base de datos
heroku config:set DATABASE_CLIENT=postgres
```

#### Procfile

```
# Procfile
web: npm start
release: npm run build
```

#### Despliegue

```bash
# Desplegar
git push heroku main

# Ver logs
heroku logs --tail
```

### 2. DigitalOcean App Platform

#### Archivo de Configuración

```yaml
# .do/app.yaml
name: scmp-api
services:
- name: api
  source_dir: /
  github:
    repo: your-username/scmp-cm
    branch: main
  run_command: npm start
  build_command: npm run build
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  env:
  - key: NODE_ENV
    value: production
  - key: APP_KEYS
    value: ${APP_KEYS}
  - key: API_TOKEN_SALT
    value: ${API_TOKEN_SALT}
  - key: ADMIN_JWT_SECRET
    value: ${ADMIN_JWT_SECRET}
  - key: JWT_SECRET
    value: ${JWT_SECRET}
  - key: DATABASE_URL
    value: ${db.DATABASE_URL}
databases:
- name: db
  engine: PG
  version: "15"
  size: basic-xs
```

### 3. AWS (usando Elastic Beanstalk)

#### Preparación

```bash
# Instalar EB CLI
pip install awsebcli

# Inicializar aplicación
eb init scmp-api

# Crear entorno
eb create production
```

#### Configuración

```yaml
# .ebextensions/01-node-command.config
option_settings:
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    NPM_CONFIG_PRODUCTION: false
```

## Monitoreo y Logging

### 1. Configuración de Logs

```javascript
// config/logger.js
module.exports = {
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: 'json',
  transports: [
    {
      type: 'file',
      filename: 'logs/app.log',
      level: 'info',
      maxsize: 10485760, // 10MB
      maxFiles: 5
    },
    {
      type: 'file',
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 10485760,
      maxFiles: 5
    }
  ]
};
```

### 2. Monitoreo con PM2

```bash
# Instalar PM2 Plus para monitoreo
pm2 install pm2-server-monit

# Configurar monitoreo
pm2 monitor

# Ver métricas
pm2 monit
```

### 3. Health Checks

```javascript
// src/api/health/routes/health.js
module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/health',
      handler: 'health.check',
      config: {
        auth: false
      }
    }
  ]
};

// src/api/health/controllers/health.js
module.exports = {
  async check(ctx) {
    const dbConnection = strapi.db.connection;
    
    try {
      await dbConnection.raw('SELECT 1');
      
      ctx.body = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        database: 'connected'
      };
    } catch (error) {
      ctx.status = 503;
      ctx.body = {
        status: 'error',
        timestamp: new Date().toISOString(),
        database: 'disconnected',
        error: error.message
      };
    }
  }
};
```

## Backup y Recuperación

### 1. Backup de Base de Datos

```bash
#!/bin/bash
# scripts/backup-db.sh

DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/var/backups/scmp"
DB_NAME="scmp_production"
DB_USER="scmp_user"

# Crear directorio de backup
mkdir -p $BACKUP_DIR

# Crear backup
pg_dump -h localhost -U $DB_USER -d $DB_NAME > $BACKUP_DIR/scmp_$DATE.sql

# Comprimir backup
gzip $BACKUP_DIR/scmp_$DATE.sql

# Eliminar backups antiguos (mantener últimos 7 días)
find $BACKUP_DIR -name "scmp_*.sql.gz" -mtime +7 -delete

echo "Backup completado: scmp_$DATE.sql.gz"
```

### 2. Backup de Archivos

```bash
#!/bin/bash
# scripts/backup-files.sh

DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/var/backups/scmp"
APP_DIR="/var/www/scmp"

# Backup de uploads
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $APP_DIR public/uploads

# Backup de configuración
tar -czf $BACKUP_DIR/config_$DATE.tar.gz -C $APP_DIR .env.production config/

echo "Backup de archivos completado"
```

### 3. Automatización de Backups

```bash
# Agregar a crontab
crontab -e

# Backup diario a las 2 AM
0 2 * * * /var/www/scmp/scripts/backup-db.sh
0 2 * * * /var/www/scmp/scripts/backup-files.sh

# Backup semanal completo los domingos a las 3 AM
0 3 * * 0 /var/www/scmp/scripts/full-backup.sh
```

## Seguridad

### 1. Firewall

```bash
# Configurar UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. Fail2Ban

```bash
# Instalar Fail2Ban
sudo apt install -y fail2ban

# Configurar para Nginx
sudo nano /etc/fail2ban/jail.local
```

```ini
# /etc/fail2ban/jail.local
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
```

### 3. Actualizaciones de Seguridad

```bash
# Configurar actualizaciones automáticas
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## Troubleshooting

### Problemas Comunes

#### 1. Error de Memoria

```bash
# Verificar uso de memoria
free -h

# Verificar procesos
top

# Reiniciar aplicación
pm2 restart scmp-api
```

#### 2. Error de Base de Datos

```bash
# Verificar estado de PostgreSQL
sudo systemctl status postgresql

# Ver logs de PostgreSQL
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# Verificar conexiones
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

#### 3. Error de Nginx

```bash
# Verificar configuración
sudo nginx -t

# Ver logs de error
sudo tail -f /var/log/nginx/error.log

# Recargar configuración
sudo systemctl reload nginx
```

## Checklist de Despliegue

### Pre-despliegue
- [ ] Variables de entorno configuradas
- [ ] Claves de seguridad generadas
- [ ] Base de datos configurada
- [ ] SSL certificado obtenido
- [ ] Backup de datos existentes
- [ ] Tests pasando
- [ ] Documentación actualizada

### Durante el Despliegue
- [ ] Aplicación construida correctamente
- [ ] Migraciones ejecutadas
- [ ] Servicios iniciados
- [ ] Health checks pasando
- [ ] Logs sin errores
- [ ] Performance aceptable

### Post-despliegue
- [ ] Funcionalidad verificada
- [ ] Monitoreo configurado
- [ ] Backups programados
- [ ] Alertas configuradas
- [ ] Documentación de rollback preparada
- [ ] Equipo notificado

## Rollback

### Plan de Rollback

```bash
#!/bin/bash
# scripts/rollback.sh

PREVIOUS_VERSION=$1

if [ -z "$PREVIOUS_VERSION" ]; then
  echo "Uso: ./rollback.sh <version>"
  exit 1
fi

echo "Iniciando rollback a versión $PREVIOUS_VERSION..."

# Detener aplicación
pm2 stop scmp-api

# Cambiar a versión anterior
git checkout $PREVIOUS_VERSION

# Instalar dependencias
npm ci --only=production

# Construir aplicación
npm run build

# Iniciar aplicación
pm2 start scmp-api

echo "Rollback completado"
```

## Changelog

### v1.0.0 (2025-01-28)
- ✅ Guía completa de despliegue
- ✅ Configuración para VPS y Docker
- ✅ Integración con servicios cloud
- ✅ Monitoreo y logging
- ✅ Backup y recuperación
- ✅ Seguridad y troubleshooting

## Recursos Adicionales

- [Strapi Deployment Guide](https://docs.strapi.io/dev-docs/deployment)
- [PM2 Documentation](https://pm2.keymetrics.io/docs/)
- [Nginx Configuration](https://nginx.org/en/docs/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)