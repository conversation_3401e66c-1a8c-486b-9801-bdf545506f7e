{"kind": "collectionType", "collectionName": "participants", "info": {"singularName": "participant", "pluralName": "participants", "displayName": "Participant", "description": "Individuos que asisten o son invitados a eventos."}, "options": {"draftAndPublish": false}, "attributes": {"firstName": {"type": "string", "required": true}, "lastName": {"type": "string", "required": true}, "email": {"type": "email", "unique": true, "required": true}, "phone": {"type": "string"}, "ecclesiasticalRole": {"type": "string"}, "avatar": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "userAccount": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "participantProfile"}, "fieldAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::field.field"}, "zoneAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::zone.zone"}, "districtAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::district.district"}, "churchAssignment": {"type": "relation", "relation": "manyToOne", "target": "api::church.church"}}}